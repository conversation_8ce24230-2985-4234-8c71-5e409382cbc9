import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from tqdm import tqdm
import json
import time
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

# 导入模型
from models.self_attention_model import SelfAttentionModel
from models.cross_attention_model import CrossAttentionModel
from models.cube_mlp_model import CubeMLPModel
from models.gru_bilstm_model import GRUBiLSTMModel
from models.bilstm_model import BiLSTMModel
from models.mult_model import MulTModel
from models.pmr_model import PMRModel

# 导入数据处理函数
from temporal_data_processor import prepare_temporal_data

# 定义损失函数
class FocalLoss(nn.Module):
    """Focal Loss，用于处理类别不平衡问题"""
    def __init__(self, gamma=2.0, weight=None):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.weight = weight
        self.ce_loss = nn.CrossEntropyLoss(weight=weight, reduction='none')

    def forward(self, input, target):
        logp = self.ce_loss(input, target)
        p = torch.exp(-logp)
        loss = (1 - p) ** self.gamma * logp
        return loss.mean()

def get_class_weights(train_loader, symptom_types, device):
    """计算类别权重，用于处理类别不平衡问题"""
    class_counts = {symptom: {0: 0, 1: 0} for symptom in range(len(symptom_types))}

    for _, _, _, labels in train_loader:
        for i, label in enumerate(labels):
            for cls in [0, 1]:
                class_counts[i][cls] += (label == cls).sum().item()

    class_weights = {}
    for symptom in range(len(symptom_types)):
        total = class_counts[symptom][0] + class_counts[symptom][1]
        if total > 0 and class_counts[symptom][0] > 0 and class_counts[symptom][1] > 0:
            weight_0 = total / (2 * class_counts[symptom][0])
            weight_1 = total / (2 * class_counts[symptom][1])
            class_weights[symptom] = torch.tensor([weight_0, weight_1], device=device)
        else:
            class_weights[symptom] = torch.tensor([1.0, 1.0], device=device)

    return class_weights

def train_model(model, train_loader, val_loader, symptom_types, device,
               learning_rate=0.0005, weight_decay=1e-5, epochs=100,
               patience=15, min_delta=0.001, model_name="model",
               class_weight=None, focal_loss_gamma=2.0,
               use_focal_loss=True, use_scheduler=True):
    """
    训练模型

    参数:
    - model: 模型实例
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - symptom_types: 症状类型列表
    - device: 训练设备
    - learning_rate: 初始学习率
    - weight_decay: 权重衰减系数
    - epochs: 最大训练轮数
    - patience: 早停耐心值
    - min_delta: 早停最小改进阈值
    - model_name: 模型名称，用于保存
    - class_weight: 类别权重，用于处理类别不平衡
    - focal_loss_gamma: Focal Loss的gamma参数
    - use_focal_loss: 是否使用Focal Loss
    - use_scheduler: 是否使用学习率调度器

    返回:
    - 训练后的模型
    - 训练历史记录
    """
    # 将模型移至设备
    model = model.to(device)

    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)

    # 学习率调度器
    if use_scheduler:
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5,
            min_lr=1e-7
        )

    # 计算类别权重
    if class_weight is None:
        class_weights = get_class_weights(train_loader, symptom_types, device)
    else:
        class_weights = {i: class_weight for i in range(len(symptom_types))}

    # 初始化训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rates': [],
    }

    for symptom in symptom_types:
        history[f'{symptom}_val_acc'] = []
        history[f'{symptom}_val_precision'] = []
        history[f'{symptom}_val_recall'] = []
        history[f'{symptom}_val_f1'] = []
        history[f'{symptom}_val_auc'] = []
        history[f'{symptom}_val_threshold'] = []

    # 早停变量
    best_val_loss = float('inf')
    best_epoch = -1
    best_model_state = None
    no_improve_count = 0

    print(f"开始训练 {model_name} 模型...")

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0

        for visual, audio, text, labels in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} - Training"):
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)
            labels = [label.to(device) for label in labels]  # 每个症状的标签

            # 前向传播
            outputs = model(visual, audio, text)

            # 计算每个症状的损失并求和
            loss = 0
            for i, (output, label) in enumerate(zip(outputs, labels)):
                symptom = i
                if use_focal_loss:
                    criterion = FocalLoss(gamma=focal_loss_gamma, weight=class_weights[symptom])
                    loss += criterion(output, label)
                else:
                    criterion = nn.CrossEntropyLoss(weight=class_weights[symptom])
                    loss += criterion(output, label)

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            train_loss += loss.item()

        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        history['learning_rates'].append(optimizer.param_groups[0]['lr'])

        # 验证阶段
        model.eval()
        val_loss = 0.0
        all_preds = {symptom: [] for symptom in range(len(symptom_types))}
        all_labels = {symptom: [] for symptom in range(len(symptom_types))}
        all_probs = {symptom: [] for symptom in range(len(symptom_types))}  # 存储预测概率

        with torch.no_grad():
            for visual, audio, text, labels in tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} - Validation"):
                visual, audio, text = visual.to(device), audio.to(device), text.to(device)
                target_labels = [label.to(device) for label in labels]

                # 前向传播
                outputs = model(visual, audio, text)

                # 计算损失
                batch_loss = 0
                for i, (output, label) in enumerate(zip(outputs, target_labels)):
                    symptom = i
                    if use_focal_loss:
                        criterion = FocalLoss(gamma=focal_loss_gamma, weight=class_weights[symptom])
                        batch_loss += criterion(output, label)
                    else:
                        criterion = nn.CrossEntropyLoss(weight=class_weights[symptom])
                        batch_loss += criterion(output, label)

                    # 获取预测结果
                    probs = torch.softmax(output, dim=1)[:, 1]  # 获取正类的概率
                    preds = torch.argmax(output, dim=1)

                    # 收集预测结果和标签
                    all_preds[symptom].extend(preds.cpu().numpy())
                    all_labels[symptom].extend(label.cpu().numpy())
                    all_probs[symptom].extend(probs.cpu().numpy())

                val_loss += batch_loss.item()

        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)

        # 更新学习率
        if use_scheduler:
            scheduler.step(val_loss)

        # 计算各症状的评估指标
        print(f"Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        print("验证集评估指标:")

        for i, symptom_name in enumerate(symptom_types):
            symptom = i

            # 寻找最佳阈值
            best_threshold = 0.5
            best_f1 = 0

            for threshold in np.arange(0.3, 0.8, 0.05):
                threshold_preds = (np.array(all_probs[symptom]) >= threshold).astype(int)
                f1 = f1_score(all_labels[symptom], threshold_preds, zero_division=0)

                if f1 > best_f1:
                    best_f1 = f1
                    best_threshold = threshold

            # 使用最佳阈值计算指标
            threshold_preds = (np.array(all_probs[symptom]) >= best_threshold).astype(int)
            acc = accuracy_score(all_labels[symptom], threshold_preds)
            prec = precision_score(all_labels[symptom], threshold_preds, zero_division=0)
            rec = recall_score(all_labels[symptom], threshold_preds, zero_division=0)
            f1 = f1_score(all_labels[symptom], threshold_preds, zero_division=0)

            # 计算AUC
            if len(np.unique(all_labels[symptom])) > 1:
                roc_auc = roc_auc_score(all_labels[symptom], all_probs[symptom])
            else:
                roc_auc = 0.5  # 如果只有一个类别，AUC默认为0.5

            # 保存指标
            history[f'{symptom_name}_val_acc'].append(acc)
            history[f'{symptom_name}_val_precision'].append(prec)
            history[f'{symptom_name}_val_recall'].append(rec)
            history[f'{symptom_name}_val_f1'].append(f1)
            history[f'{symptom_name}_val_auc'].append(roc_auc)
            history[f'{symptom_name}_val_threshold'].append(best_threshold)

            print(f"  {symptom_name} - Acc: {acc:.4f}, Precision: {prec:.4f}, Recall: {rec:.4f}, F1: {f1:.4f}, AUC: {roc_auc:.4f}, Threshold: {best_threshold:.4f}")

        # 早停检查
        if val_loss < best_val_loss - min_delta:
            best_val_loss = val_loss
            best_epoch = epoch
            best_model_state = model.state_dict().copy()
            no_improve_count = 0

            # 保存最佳模型
            os.makedirs('model_checkpoints', exist_ok=True)
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'history': history,
            }, f'model_checkpoints/{model_name}_best.pth')

            print(f"保存最佳模型，验证损失: {val_loss:.4f}")
        else:
            no_improve_count += 1

        # 早停检查
        if no_improve_count >= patience:
            print(f"早停触发！已经 {patience} 轮验证损失未改善")
            break

    # 恢复最佳模型
    print(f"使用第 {best_epoch+1} 轮的最佳模型，验证损失: {best_val_loss:.4f}")
    model.load_state_dict(best_model_state)

    return model, history

def evaluate_model(model, test_loader, symptom_types, device, thresholds=None):
    """
    评估模型

    参数:
    - model: 模型实例
    - test_loader: 测试数据加载器
    - symptom_types: 症状类型列表
    - device: 评估设备
    - thresholds: 各症状的预测阈值，如果为None则使用0.5

    返回:
    - 评估指标字典
    """
    model.eval()

    # 初始化结果收集器
    all_preds = {symptom: [] for symptom in range(len(symptom_types))}
    all_labels = {symptom: [] for symptom in range(len(symptom_types))}
    all_probs = {symptom: [] for symptom in range(len(symptom_types))}

    # 如果没有提供阈值，使用默认值0.5
    if thresholds is None:
        thresholds = {symptom: 0.5 for symptom in range(len(symptom_types))}

    with torch.no_grad():
        for visual, audio, text, labels in tqdm(test_loader, desc="Evaluating"):
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)

            # 前向传播
            outputs = model(visual, audio, text)

            # 收集预测结果和标签
            for i, (output, label) in enumerate(zip(outputs, labels)):
                symptom = i
                probs = torch.softmax(output, dim=1)[:, 1]  # 获取正类的概率
                preds = (probs >= thresholds[symptom]).int()

                all_preds[symptom].extend(preds.cpu().numpy())
                all_labels[symptom].extend(label.cpu().numpy())
                all_probs[symptom].extend(probs.cpu().numpy())

    # 计算各症状的评估指标
    results = {
        'overall': {
            'accuracy': 0,
            'precision': 0,
            'recall': 0,
            'f1': 0,
            'auc': 0
        }
    }

    for i, symptom_name in enumerate(symptom_types):
        symptom = i

        # 计算指标
        acc = accuracy_score(all_labels[symptom], all_preds[symptom])
        prec = precision_score(all_labels[symptom], all_preds[symptom], zero_division=0)
        rec = recall_score(all_labels[symptom], all_preds[symptom], zero_division=0)
        f1 = f1_score(all_labels[symptom], all_preds[symptom], zero_division=0)

        # 计算AUC
        if len(np.unique(all_labels[symptom])) > 1:
            roc_auc = roc_auc_score(all_labels[symptom], all_probs[symptom])
        else:
            roc_auc = 0.5  # 如果只有一个类别，AUC默认为0.5

        # 保存指标
        results[symptom_name] = {
            'accuracy': float(acc),
            'precision': float(prec),
            'recall': float(rec),
            'f1': float(f1),
            'auc': float(roc_auc)
        }

        # 累加到总体指标
        results['overall']['accuracy'] += acc
        results['overall']['precision'] += prec
        results['overall']['recall'] += rec
        results['overall']['f1'] += f1
        results['overall']['auc'] += roc_auc

    # 计算总体平均指标
    num_symptoms = len(symptom_types)
    results['overall']['accuracy'] /= num_symptoms
    results['overall']['precision'] /= num_symptoms
    results['overall']['recall'] /= num_symptoms
    results['overall']['f1'] /= num_symptoms
    results['overall']['auc'] /= num_symptoms

    return results

def main():
    parser = argparse.ArgumentParser(description='训练不同的模型变体')

    # 数据参数
    parser.add_argument('--data_dir', type=str, default='/Users/<USER>/Downloads/CMDC_ATV/shuju', help='数据目录')
    parser.add_argument('--label_file', type=str, default='/Users/<USER>/Downloads/CMDC_ATV/symptom_label_template.xlsx', help='标签文件路径')
    parser.add_argument('--max_seq_len', type=int, default=100, help='最大序列长度')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')

    # 训练参数
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.0005, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='权重衰减')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=0.001, help='早停最小改进阈值')
    parser.add_argument('--use_focal_loss', action='store_true', help='是否使用Focal Loss')
    parser.add_argument('--focal_loss_gamma', type=float, default=2.0, help='Focal Loss的gamma参数')

    # 模型参数
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--num_heads', type=int, default=4, help='注意力头数量')
    parser.add_argument('--num_layers', type=int, default=2, help='层数')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout比率')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='训练设备')
    parser.add_argument('--augment_prob', type=float, default=0.2, help='数据增强概率')
    parser.add_argument('--model_type', type=str, default='all',
                        choices=['self_attention', 'cross_attention', 'cube_mlp', 'gru_bilstm', 'bilstm', 'mult', 'pmr', 'all'],
                        help='要训练的模型类型')

    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)

    # 症状类型
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']

    # 准备数据
    print("准备数据...")
    train_loader, val_loader, test_loader, _ = prepare_temporal_data(
        data_dir=args.data_dir,
        label_file=args.label_file,
        symptom_types=symptom_types,
        max_seq_len=args.max_seq_len,
        batch_size=args.batch_size,
        augment_prob=args.augment_prob,
        seed=args.seed
    )

    # 获取特征维度
    for visual, audio, text, _ in train_loader:
        visual_dim = visual.shape[2] if len(visual.shape) > 2 else visual.shape[1]
        audio_dim = audio.shape[2] if len(audio.shape) > 2 else audio.shape[1]
        text_dim = text.shape[2] if len(text.shape) > 2 else text.shape[1]
        print(f"特征维度 - 视觉: {visual_dim}, 音频: {audio_dim}, 文本: {text_dim}")
        break

    # 定义要训练的模型
    model_types = []
    if args.model_type == 'all':
        model_types = ['self_attention', 'cross_attention', 'cube_mlp', 'gru_bilstm', 'bilstm', 'mult', 'pmr']
    else:
        model_types = [args.model_type]

    # 创建结果目录
    os.makedirs('ablation_results', exist_ok=True)

    # 训练和评估每个模型
    all_results = {}

    for model_type in model_types:
        print(f"\n{'='*50}")
        print(f"训练模型: {model_type}")
        print(f"{'='*50}\n")

        # 创建模型
        if model_type == 'self_attention':
            model = SelfAttentionModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_heads=args.num_heads,
                dropout=args.dropout
            )
        elif model_type == 'cross_attention':
            model = CrossAttentionModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_heads=args.num_heads,
                dropout=args.dropout
            )
        elif model_type == 'cube_mlp':
            model = CubeMLPModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_layers=args.num_layers,
                dropout=args.dropout
            )
        elif model_type == 'gru_bilstm':
            model = GRUBiLSTMModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_layers=args.num_layers,
                dropout=args.dropout
            )
        elif model_type == 'bilstm':
            model = BiLSTMModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_layers=args.num_layers,
                dropout=args.dropout
            )
        elif model_type == 'mult':
            model = MulTModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_layers=args.num_layers,
                num_heads=args.num_heads,
                dropout=args.dropout
            )
        elif model_type == 'pmr':
            model = PMRModel(
                visual_dim=visual_dim,
                audio_dim=audio_dim,
                text_dim=text_dim,
                hidden_dim=args.hidden_dim,
                num_symptoms=len(symptom_types),
                num_reasoning_steps=args.num_layers,
                dropout=args.dropout
            )

        # 训练模型
        trained_model, history = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            symptom_types=symptom_types,
            device=args.device,
            learning_rate=args.learning_rate,
            weight_decay=args.weight_decay,
            epochs=args.epochs,
            patience=args.patience,
            min_delta=args.min_delta,
            model_name=model_type,
            focal_loss_gamma=args.focal_loss_gamma,
            use_focal_loss=args.use_focal_loss
        )

        # 获取最佳阈值
        best_epoch_idx = history['val_loss'].index(min(history['val_loss']))
        thresholds = {}
        for i, symptom in enumerate(symptom_types):
            thresholds[i] = history[f'{symptom}_val_threshold'][best_epoch_idx]

        # 评估模型
        print(f"\n评估模型: {model_type}")
        results = evaluate_model(
            model=trained_model,
            test_loader=test_loader,
            symptom_types=symptom_types,
            device=args.device,
            thresholds=thresholds
        )

        # 保存结果
        all_results[model_type] = results

        # 打印结果
        print(f"\n{model_type} 模型测试结果:")
        print(f"总体指标 - 准确率: {results['overall']['accuracy']:.4f}, 精确率: {results['overall']['precision']:.4f}, 召回率: {results['overall']['recall']:.4f}, F1: {results['overall']['f1']:.4f}, AUC: {results['overall']['auc']:.4f}")

        for symptom in symptom_types:
            print(f"{symptom} - 准确率: {results[symptom]['accuracy']:.4f}, 精确率: {results[symptom]['precision']:.4f}, 召回率: {results[symptom]['recall']:.4f}, F1: {results[symptom]['f1']:.4f}, AUC: {results[symptom]['auc']:.4f}")

        # 保存结果到JSON文件
        with open(f'ablation_results/{model_type}_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=4)

        # 保存训练历史到JSON文件
        with open(f'ablation_results/{model_type}_history.json', 'w', encoding='utf-8') as f:
            # 将numpy数组转换为列表
            history_serializable = {}
            for key, value in history.items():
                if isinstance(value, list) and len(value) > 0 and isinstance(value[0], (np.float32, np.float64)):
                    history_serializable[key] = [float(v) for v in value]
                else:
                    history_serializable[key] = value

            json.dump(history_serializable, f, ensure_ascii=False, indent=4)

    # 保存所有结果到一个JSON文件
    with open('ablation_results/all_model_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=4)

    print("\n所有模型训练和评估完成！")

if __name__ == '__main__':
    main()

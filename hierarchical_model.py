import torch
import torch.nn as nn
import torch.nn.functional as F

class CrossAttention(nn.Module):
    """跨模态注意力机制"""
    def __init__(self, dim):
        super(CrossAttention, self).__init__()
        self.query = nn.Linear(dim, dim)
        self.key = nn.Linear(dim, dim)
        self.value = nn.Linear(dim, dim)
        self.scale = dim ** -0.5
        
    def forward(self, q, kv):
        """
        参数:
        - q: 查询张量 [batch_size, seq_len_q, dim]
        - kv: 键值张量 [batch_size, seq_len_kv, dim]
        
        返回:
        - 注意力输出 [batch_size, seq_len_q, dim]
        """
        query = self.query(q)
        key = self.key(kv)
        value = self.value(kv)
        
        # 计算注意力分数
        attn = torch.matmul(query, key.transpose(-2, -1)) * self.scale
        attn = F.softmax(attn, dim=-1)
        
        # 应用注意力权重
        out = torch.matmul(attn, value)
        
        return out

class HierarchicalSymptomModel(nn.Module):
    """分层症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256):
        super(HierarchicalSymptomModel, self).__init__()
        
        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 跨模态注意力模块
        self.v2a_attention = CrossAttention(hidden_dim)
        self.v2t_attention = CrossAttention(hidden_dim)
        self.a2v_attention = CrossAttention(hidden_dim)
        self.a2t_attention = CrossAttention(hidden_dim)
        self.t2v_attention = CrossAttention(hidden_dim)
        self.t2a_attention = CrossAttention(hidden_dim)
        
        # 自适应融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(hidden_dim * 9, hidden_dim * 3),
            nn.LayerNorm(hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim * 3, hidden_dim * 3),
            nn.Sigmoid()
        )
        
        # 融合后的特征处理
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 残差连接
        self.residual = nn.Linear(hidden_dim * 3, hidden_dim)
        
        # 第一层分类器 - 判断是否有任何症状
        self.presence_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim // 2, 2)  # 二分类：有症状 vs 无症状
        )
        
        # 第二层分类器 - 细分症状类型
        self.symptom_classifiers = nn.ModuleDict({
            '情绪症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim // 2, 2)
            ),
            '躯体症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim // 2, 2)
            ),
            '认知症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim // 2, 2)
            ),
            '行为症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim // 2, 2)
            )
        })
        
        # 症状特定注意力
        self.symptom_attentions = nn.ModuleDict({
            '情绪症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '躯体症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '认知症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '行为症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            )
        })
    
    def forward(self, visual, audio, text, training=True):
        batch_size = visual.size(0)
        
        # 编码特征
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 创建特征序列形式，适配注意力机制
        v_seq = visual_encoded.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        a_seq = audio_encoded.unsqueeze(1)
        t_seq = text_encoded.unsqueeze(1)
        
        # 应用跨模态注意力
        v2a = self.v2a_attention(v_seq, a_seq).squeeze(1)  # [batch_size, hidden_dim]
        v2t = self.v2t_attention(v_seq, t_seq).squeeze(1)
        a2v = self.a2v_attention(a_seq, v_seq).squeeze(1)
        a2t = self.a2t_attention(a_seq, t_seq).squeeze(1)
        t2v = self.t2v_attention(t_seq, v_seq).squeeze(1)
        t2a = self.t2a_attention(t_seq, a_seq).squeeze(1)
        
        # 融合特征 - 包含所有交叉注意力结果
        concat_feat = torch.cat([
            visual_encoded, audio_encoded, text_encoded,
            v2a, v2t, a2v, a2t, t2v, t2a
        ], dim=1)
        
        gate = self.fusion_gate(concat_feat)
        
        # 应用自适应门控
        hidden_dim = visual_encoded.size(1)
        gated_features = torch.cat([
            visual_encoded * gate[:, :hidden_dim],
            audio_encoded * gate[:, hidden_dim:2*hidden_dim],
            text_encoded * gate[:, 2*hidden_dim:3*hidden_dim]
        ], dim=1)
        
        # 最终融合
        fused = self.fusion(gated_features)
        
        # 残差连接
        residual_features = self.residual(torch.cat([visual_encoded, audio_encoded, text_encoded], dim=1))
        fused = fused + residual_features
        
        # 第一层分类 - 判断是否有任何症状
        presence_logits = self.presence_classifier(fused)
        presence_probs = F.softmax(presence_logits, dim=1)
        
        # 第二层分类 - 细分症状类型
        symptom_outputs = {}
        
        for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']:
            # 应用症状特定注意力
            symptom_attn = self.symptom_attentions[symptom](fused)
            symptom_features = fused * symptom_attn
            
            # 症状特定分类
            symptom_logits = self.symptom_classifiers[symptom](symptom_features)
            
            if training:
                # 训练时返回logits
                symptom_outputs[symptom] = (presence_logits, symptom_logits)
            else:
                # 推理时，根据第一层结果决定第二层输出
                symptom_probs = F.softmax(symptom_logits, dim=1)
                # 如果第一层预测有症状，则使用第二层的预测；否则预测无症状
                has_any_symptom = (presence_probs[:, 1] > 0.5).float().unsqueeze(1)
                final_probs = has_any_symptom * symptom_probs[:, 1].unsqueeze(1)
                symptom_outputs[symptom] = final_probs
        
        # 转换输出为列表，保持与原模型兼容
        if training:
            # 训练时，返回两层分类器的输出
            output_list = [symptom_outputs[symptom] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
        else:
            # 推理时，返回最终预测概率
            output_list = [symptom_outputs[symptom] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
        
        return output_list

# 分层损失函数
class HierarchicalSymptomLoss(nn.Module):
    """分层症状损失函数"""
    def __init__(self, presence_weight=0.3, type_weight=0.7):
        super(HierarchicalSymptomLoss, self).__init__()
        self.presence_weight = presence_weight
        self.type_weight = type_weight
        self.ce_loss = nn.CrossEntropyLoss()
        
    def forward(self, outputs, targets):
        presence_logits, type_logits = outputs
        
        # 症状存在性损失 - 二分类：有症状(1) vs 无症状(0)
        # 如果任何症状为1，则存在性标签为1，否则为0
        presence_target = (targets > 0).long()
        presence_loss = self.ce_loss(presence_logits, presence_target)
        
        # 症状类型损失 - 二分类：特定症状 vs 其他
        type_loss = self.ce_loss(type_logits, targets)
        
        # 总损失 = 存在性损失 * 权重 + 类型损失 * 权重
        total_loss = self.presence_weight * presence_loss + self.type_weight * type_loss
        
        return total_loss

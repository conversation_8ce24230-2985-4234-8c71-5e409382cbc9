import os
import torch
import numpy as np
import pandas as pd
import json
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import argparse
from tqdm import tqdm

# 导入优化的组件
from advanced_features import AdvancedFeatureExtractor
from advanced_model import AdvancedMultiSymptomModel
from advanced_training import train_model, evaluate_model

# 设置随机种子
def set_seed(seed):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 优化的数据集类
class OptimizedDataset(Dataset):
    """优化的数据集类"""
    def __init__(self, data_dir, samples, symptom_types, 
                 text_augment_prob=0.0, audio_augment_prob=0.0, video_augment_prob=0.0):
        """
        初始化数据集
        
        参数:
        - data_dir: 特征数据目录
        - samples: 样本列表 [(sample_id, question_num, labels), ...]
        - symptom_types: 症状类型列表
        - text_augment_prob: 文本增强概率
        - audio_augment_prob: 音频增强概率
        - video_augment_prob: 视频增强概率
        """
        self.data_dir = data_dir
        self.samples = samples
        self.symptom_types = symptom_types
        
        self.text_augment_prob = text_augment_prob
        self.audio_augment_prob = audio_augment_prob
        self.video_augment_prob = video_augment_prob
        
        # 特征提取器
        self.feature_extractor = AdvancedFeatureExtractor(data_dir)
        
        # 缓存
        self.cache = {}
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        # 检查缓存
        if idx in self.cache:
            return self.cache[idx]
        
        sample_id, question_num, labels = self.samples[idx]
        
        # 提取特征
        visual_feature, audio_feature, text_feature = self.feature_extractor.extract_features(sample_id, question_num)
        
        # 应用增强
        augmented_visual = visual_feature.copy()
        augmented_audio = audio_feature.copy()
        augmented_text = text_feature.copy()
        
        # 视频特征扰动
        if np.random.random() < self.video_augment_prob:
            augmented_visual = self._augment_visual_feature(visual_feature)
        
        # 音频特征扰动
        if np.random.random() < self.audio_augment_prob:
            augmented_audio = self._augment_audio_feature(audio_feature)
        
        # 文本特征扰动
        if np.random.random() < self.text_augment_prob:
            augmented_text = self._augment_text_feature(text_feature)
        
        # 转换为张量
        visual_tensor = torch.tensor(augmented_visual, dtype=torch.float32)
        audio_tensor = torch.tensor(augmented_audio, dtype=torch.float32)
        text_tensor = torch.tensor(augmented_text, dtype=torch.float32)
        
        # 转换标签为张量
        label_tensors = [torch.tensor(label, dtype=torch.long) for label in labels]
        
        # 缓存结果
        result = (visual_tensor, audio_tensor, text_tensor, label_tensors)
        self.cache[idx] = result
        
        return result
    
    def _augment_visual_feature(self, visual_feature):
        """视觉特征增强"""
        # 复制原始特征
        augmented_feature = visual_feature.copy()
        
        # 添加小幅度噪声
        noise_scale = 0.02  # 2%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise
        
        # 随机特征掩码
        mask_ratio = 0.05  # 5%的特征被掩码
        mask = np.random.rand(*augmented_feature.shape) > mask_ratio
        augmented_feature = augmented_feature * mask
        
        # 特征缩放
        scale_factor = np.random.uniform(0.95, 1.05)
        augmented_feature = augmented_feature * scale_factor
        
        # 一致性检测 - 余弦相似度
        similarity = self._cosine_similarity(visual_feature, augmented_feature)
        
        # 如果相似度太低，则返回原始特征
        if similarity < 0.9:
            return visual_feature
            
        return augmented_feature
    
    def _augment_audio_feature(self, audio_feature):
        """音频特征增强"""
        # 复制原始特征
        augmented_feature = audio_feature.copy()
        
        # 添加小幅度噪声
        noise_scale = 0.02  # 2%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise
        
        # 随机特征掩码
        mask_ratio = 0.05  # 5%的特征被掩码
        mask = np.random.rand(*augmented_feature.shape) > mask_ratio
        augmented_feature = augmented_feature * mask
        
        # 特征缩放
        scale_factor = np.random.uniform(0.95, 1.05)
        augmented_feature = augmented_feature * scale_factor
        
        # 一致性检测 - 余弦相似度
        similarity = self._cosine_similarity(audio_feature, augmented_feature)
        
        # 如果相似度太低，则返回原始特征
        if similarity < 0.9:
            return audio_feature
            
        return augmented_feature
    
    def _augment_text_feature(self, text_feature):
        """文本特征增强"""
        # 复制原始特征
        augmented_feature = text_feature.copy()
        
        # 添加小幅度噪声
        noise_scale = 0.01  # 1%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise
        
        # 随机特征掩码
        mask_ratio = 0.03  # 3%的特征被掩码
        mask = np.random.rand(*augmented_feature.shape) > mask_ratio
        augmented_feature = augmented_feature * mask
        
        # 一致性检测 - 余弦相似度
        similarity = self._cosine_similarity(text_feature, augmented_feature)
        
        # 如果相似度太低，则返回原始特征
        if similarity < 0.95:
            return text_feature
            
        return augmented_feature
    
    def _cosine_similarity(self, a, b):
        """计算余弦相似度"""
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b) + 1e-8)

# 创建数据加载器
def create_optimized_dataloader(data_dir, samples, symptom_types, 
                               text_augment_prob=0.0, audio_augment_prob=0.0, video_augment_prob=0.0,
                               batch_size=32, shuffle=True):
    """创建优化的数据加载器"""
    dataset = OptimizedDataset(
        data_dir=data_dir,
        samples=samples,
        symptom_types=symptom_types,
        text_augment_prob=text_augment_prob,
        audio_augment_prob=audio_augment_prob,
        video_augment_prob=video_augment_prob
    )
    
    return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)

# 准备数据
def prepare_optimized_data(data_dir, label_file, symptom_types, 
                          batch_size=32, test_size=0.15, val_size=0.15, seed=42,
                          text_augment_prob=0.2, audio_augment_prob=0.2, video_augment_prob=0.2):
    """准备优化的训练、验证和测试数据加载器"""
    # 设置随机种子
    set_seed(seed)
    
    # 读取标签文件
    df = pd.read_excel(label_file)
    
    # 获取所有有效的样本目录
    all_sample_dirs = set()
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            all_sample_dirs.add(item)
    
    # 收集有效样本
    valid_samples = []
    
    for sample_full_id in df['SampleID'].tolist():
        # 检查目录是否存在
        if sample_full_id in all_sample_dirs:
            sample_dir = os.path.join(data_dir, sample_full_id)
            
            # 解析样本ID和问题编号
            sample_id, q_part = sample_full_id.split('_')
            question_num = int(q_part[1:])
            
            # 提取多个症状标签
            try:
                row = df[df['SampleID'] == sample_full_id].iloc[0]
                labels = [int(row[symptom]) for symptom in symptom_types]
                valid_samples.append((sample_id, question_num, labels))
                print(f"添加样本: {sample_full_id}")
            except Exception as e:
                print(f"提取标签出错 {sample_full_id}: {e}")
    
    print(f"有效样本数：{len(valid_samples)}")
    
    if len(valid_samples) == 0:
        raise ValueError("没有找到有效样本，请检查数据目录和标签文件")
    
    # 划分训练、验证和测试集
    train_val_samples, test_samples = train_test_split(valid_samples, test_size=test_size, random_state=seed)
    train_samples, val_samples = train_test_split(train_val_samples, test_size=val_size/(1-test_size), random_state=seed)
    
    print(f"训练集：{len(train_samples)}，验证集：{len(val_samples)}，测试集：{len(test_samples)}")
    
    # 创建数据加载器
    train_loader = create_optimized_dataloader(
        data_dir=data_dir,
        samples=train_samples,
        symptom_types=symptom_types,
        text_augment_prob=text_augment_prob,
        audio_augment_prob=audio_augment_prob,
        video_augment_prob=video_augment_prob,
        batch_size=batch_size,
        shuffle=True
    )
    
    # 验证和测试集不使用增强
    val_loader = create_optimized_dataloader(
        data_dir=data_dir,
        samples=val_samples,
        symptom_types=symptom_types,
        batch_size=batch_size,
        shuffle=False
    )
    
    test_loader = create_optimized_dataloader(
        data_dir=data_dir,
        samples=test_samples,
        symptom_types=symptom_types,
        batch_size=batch_size,
        shuffle=False
    )
    
    return train_loader, val_loader, test_loader, symptom_types

# 主函数
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="优化的跨模态一致性数据增强训练")
    parser.add_argument("--data_dir", type=str, default="./shuju", help="特征数据目录")
    parser.add_argument("--label_file", type=str, default="./symptom_label_template.xlsx", help="标签文件路径")
    
    # 学术规范的训练参数
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.0005, help="初始学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-5, help="权重衰减系数")
    parser.add_argument("--patience", type=int, default=15, help="早停耐心值")
    parser.add_argument("--min_delta", type=float, default=0.001, help="早停最小改进阈值")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    
    # 数据增强参数
    parser.add_argument("--text_augment_prob", type=float, default=0.2, help="文本增强概率")
    parser.add_argument("--audio_augment_prob", type=float, default=0.2, help="音频增强概率")
    parser.add_argument("--video_augment_prob", type=float, default=0.2, help="视频增强概率")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="训练设备")
    
    # 模型配置参数
    parser.add_argument("--hidden_dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--dropout_rate", type=float, default=0.3, help="Dropout比率")
    parser.add_argument("--use_focal_loss", action="store_true", default=True, help="是否使用Focal Loss")
    parser.add_argument("--focal_loss_gamma", type=float, default=2.0, help="Focal Loss的gamma参数")
    parser.add_argument("--use_scheduler", action="store_true", default=True, help="是否使用学习率调度器")
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 要预测的症状列表
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']
    
    # 准备数据
    train_loader, val_loader, test_loader, symptom_types = prepare_optimized_data(
        data_dir=args.data_dir,
        label_file=args.label_file,
        symptom_types=symptom_types,
        batch_size=args.batch_size,
        text_augment_prob=args.text_augment_prob,
        audio_augment_prob=args.audio_augment_prob,
        video_augment_prob=args.video_augment_prob,
        seed=args.seed
    )
    
    # 创建模型
    print(f"症状类型数量: {len(symptom_types)}")
    
    model = AdvancedMultiSymptomModel(
        visual_dim=128,
        audio_dim=128,
        text_dim=768,
        hidden_dim=args.hidden_dim,
        num_symptoms=len(symptom_types),
        dropout_rate=args.dropout_rate
    )
    
    model.to(device)
    print("模型已创建并移动到设备上")
    
    # 训练模型
    print("\n开始优化训练模型...")
    model, training_history = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        symptom_types=symptom_types,
        device=device,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        epochs=args.epochs,
        patience=args.patience,
        min_delta=args.min_delta,
        use_focal_loss=args.use_focal_loss,
        focal_loss_gamma=args.focal_loss_gamma,
        use_scheduler=args.use_scheduler
    )
    
    # 保存模型
    model_save_path = "optimized_model.pth"
    torch.save(model.state_dict(), model_save_path)
    print(f"\n模型已保存到 {model_save_path}")
    
    # 保存训练历史记录
    with open("optimized_training_history.json", "w", encoding="utf-8") as f:
        # 将tensor转换为可序列化的格式
        serializable_history = {}
        for key, value in training_history.items():
            if isinstance(value, list):
                # 如果值是列表，检查列表中的每个元素
                serializable_history[key] = [float(item) if isinstance(item, (torch.Tensor, np.ndarray)) else item for item in value]
            else:
                serializable_history[key] = float(value) if isinstance(value, (torch.Tensor, np.ndarray)) else value
        
        json.dump(serializable_history, f, ensure_ascii=False, indent=4)
    
    print("\n训练历史记录已保存到 optimized_training_history.json")
    
    # 在测试集上评估模型
    print("\n在测试集上评估模型...")
    test_metrics = evaluate_model(model, test_loader, symptom_types, device, optimize_thresholds=True)
    
    # 保存测试结果
    with open("optimized_test_results.json", "w", encoding="utf-8") as f:
        # 将NumPy数据类型转换为Python原生数据类型
        serializable_metrics = {}
        for symptom, metrics in test_metrics.items():
            serializable_metrics[symptom] = {}
            for metric_name, metric_value in metrics.items():
                if isinstance(metric_value, (np.number, np.ndarray)):
                    serializable_metrics[symptom][metric_name] = float(metric_value)
                else:
                    serializable_metrics[symptom][metric_name] = metric_value
        
        json.dump(serializable_metrics, f, ensure_ascii=False, indent=4)
    
    print("\n测试结果已保存到 optimized_test_results.json")

if __name__ == "__main__":
    main()

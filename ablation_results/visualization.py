import matplotlib.pyplot as plt
import numpy as np
import json
import os
import pandas as pd
from matplotlib.font_manager import FontProperties

# 设置中文字体
font = FontProperties()

# 读取所有实验结果
def load_results():
    results = {}
    base_dir = './ablation_results'

    # 读取消融实验结果
    for config_dir in os.listdir(base_dir):
        if os.path.isdir(os.path.join(base_dir, config_dir)):
            result_file = os.path.join(base_dir, config_dir, 'test_results.json')
            if os.path.exists(result_file):
                with open(result_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    results[config_dir] = data

    # 读取原始完整模型结果
    original_result_file = './combined_results/combined_test_results.json'
    if os.path.exists(original_result_file):
        with open(original_result_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 转换键名以匹配消融实验结果的格式
            converted_data = {}
            key_mapping = {
                'emotional': '情绪症状',
                'somatic': '躯体症状',
                'cognitive': '认知症状',
                'behavioral': '行为症状'
            }
            for key, value in data.items():
                if key in key_mapping:
                    converted_data[key_mapping[key]] = value
                else:
                    converted_data[key] = value
            results['完整模型（原始）'] = converted_data

    return results

# 绘制总体性能比较图
def plot_overall_performance(results):
    # 提取总体性能数据
    configs = []
    accuracy = []
    precision = []
    recall = []
    f1 = []
    auc = []

    for config, data in results.items():
        if 'average' in data:
            configs.append(config)
            accuracy.append(data['average']['accuracy'])
            precision.append(data['average']['precision'])
            recall.append(data['average']['recall'])
            f1.append(data['average']['f1'])
            auc.append(data['average']['auc'])

    # 创建DataFrame
    df = pd.DataFrame({
        'Config': configs,
        'Accuracy': accuracy,
        'Precision': precision,
        'Recall': recall,
        'F1': f1,
        'AUC': auc
    })

    # 按F1分数排序
    df = df.sort_values('F1', ascending=False)

    # 绘图
    plt.figure(figsize=(12, 8))

    x = np.arange(len(df))
    width = 0.15

    plt.bar(x - 2*width, df['Accuracy'], width, label='准确率')
    plt.bar(x - width, df['Precision'], width, label='精确率')
    plt.bar(x, df['Recall'], width, label='召回率')
    plt.bar(x + width, df['F1'], width, label='F1分数')
    plt.bar(x + 2*width, df['AUC'], width, label='AUC')

    plt.xlabel('模型配置', fontproperties=font, fontsize=12)
    plt.ylabel('性能指标', fontproperties=font, fontsize=12)
    plt.title('各模型配置总体性能比较', fontproperties=font, fontsize=14)
    plt.xticks(x, df['Config'], rotation=45, ha='right', fontproperties=font)
    plt.legend(prop=font)
    plt.tight_layout()
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    plt.savefig('./ablation_results/overall_performance.png', dpi=300)
    plt.close()

# 绘制各症状类型的F1分数比较图
def plot_symptom_f1_comparison(results):
    # 提取各症状类型的F1分数
    configs = []
    emotional_f1 = []
    somatic_f1 = []
    cognitive_f1 = []
    behavioral_f1 = []

    for config, data in results.items():
        if '情绪症状' in data and '躯体症状' in data and '认知症状' in data and '行为症状' in data:
            configs.append(config)
            emotional_f1.append(data['情绪症状']['f1'])
            somatic_f1.append(data['躯体症状']['f1'])
            cognitive_f1.append(data['认知症状']['f1'])
            behavioral_f1.append(data['行为症状']['f1'])

    # 创建DataFrame
    df = pd.DataFrame({
        'Config': configs,
        '情绪症状': emotional_f1,
        '躯体症状': somatic_f1,
        '认知症状': cognitive_f1,
        '行为症状': behavioral_f1
    })

    # 计算平均F1分数并排序
    df['平均F1'] = df[['情绪症状', '躯体症状', '认知症状', '行为症状']].mean(axis=1)
    df = df.sort_values('平均F1', ascending=False)

    # 绘图
    plt.figure(figsize=(12, 8))

    x = np.arange(len(df))
    width = 0.2

    plt.bar(x - 1.5*width, df['情绪症状'], width, label='情绪症状')
    plt.bar(x - 0.5*width, df['躯体症状'], width, label='躯体症状')
    plt.bar(x + 0.5*width, df['认知症状'], width, label='认知症状')
    plt.bar(x + 1.5*width, df['行为症状'], width, label='行为症状')

    plt.xlabel('模型配置', fontproperties=font, fontsize=12)
    plt.ylabel('F1分数', fontproperties=font, fontsize=12)
    plt.title('各模型配置在不同症状类型上的F1分数比较', fontproperties=font, fontsize=14)
    plt.xticks(x, df['Config'], rotation=45, ha='right', fontproperties=font)
    plt.legend(prop=font)
    plt.tight_layout()
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    plt.savefig('./ablation_results/symptom_f1_comparison.png', dpi=300)
    plt.close()

# 绘制雷达图比较最佳模型
def plot_radar_chart(results):
    # 选择性能最好的三个模型
    best_models = ['完整模型（原始）', '仅跨模态一致性', '仅时序增强']
    metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']

    # 提取数据
    data = {}
    for model in best_models:
        if model in results:
            data[model] = [
                results[model]['average']['accuracy'],
                results[model]['average']['precision'],
                results[model]['average']['recall'],
                results[model]['average']['f1'],
                results[model]['average']['auc']
            ]

    # 绘制雷达图
    angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))

    for model, values in data.items():
        values += values[:1]  # 闭合图形
        ax.plot(angles, values, linewidth=2, label=model)
        ax.fill(angles, values, alpha=0.1)

    # 设置标签
    metric_labels = ['准确率', '精确率', '召回率', 'F1分数', 'AUC']
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels([metric_labels[i] for i in range(len(metrics))], fontproperties=font, fontsize=12)

    plt.title('最佳模型性能雷达图比较', fontproperties=font, fontsize=14)
    plt.legend(loc='upper right', prop=font)

    plt.savefig('./ablation_results/radar_chart.png', dpi=300)
    plt.close()

if __name__ == '__main__':
    results = load_results()
    plot_overall_performance(results)
    plot_symptom_f1_comparison(results)
    plot_radar_chart(results)
    print("可视化图表已生成！")

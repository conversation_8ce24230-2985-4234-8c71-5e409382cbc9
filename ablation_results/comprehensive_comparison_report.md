# 消融实验综合比较报告

## 总体性能比较

| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
| ---- | ------ | ------ | ------ | ------ | --- |
| 仅时序增强 | 0.6898 | 0.5105 | 0.6880 | 0.5398 | 0.7428 |
| 仅跨模态一致性 | 0.8310 | 0.6893 | 0.7044 | 0.6726 | 0.8391 |
| 无自适应权重 | 0.7083 | 0.4592 | 0.6385 | 0.4994 | 0.7426 |
| 无分层分类 | 0.6620 | 0.3839 | 0.7747 | 0.5114 | 0.7240 |
| 时序+跨模态简化版 | 0.6782 | 0.4079 | 0.7252 | 0.5111 | 0.7596 |
| 完整模型（原始） | 0.8472 | 0.6861 | 0.6335 | 0.6529 | 0.8271 |

## 各症状类型性能比较

### 情绪症状

| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
| ---- | ------ | ------ | ------ | ------ | --- |
| 仅时序增强 | 0.8519 | 0.8333 | 0.6250 | 0.7143 | 0.8705 |
| 仅跨模态一致性 | 0.7870 | 0.6098 | 0.7813 | 0.6849 | 0.8549 |
| 无自适应权重 | 0.8148 | 0.6875 | 0.6875 | 0.6875 | 0.8372 |
| 无分层分类 | 0.6759 | 0.4717 | 0.7813 | 0.5882 | 0.7500 |
| 时序+跨模态简化版 | 0.6111 | 0.4219 | 0.8438 | 0.5625 | 0.7430 |
| 完整模型（原始） | 0.8704 | 0.8750 | 0.6563 | 0.7500 | 0.8808 |

### 躯体症状

| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
| ---- | ------ | ------ | ------ | ------ | --- |
| 仅时序增强 | 0.3611 | 0.2209 | 0.9048 | 0.3551 | 0.6130 |
| 仅跨模态一致性 | 0.8519 | 0.6000 | 0.7143 | 0.6522 | 0.8232 |
| 无自适应权重 | 0.7963 | 0.4667 | 0.3333 | 0.3889 | 0.6470 |
| 无分层分类 | 0.6944 | 0.3636 | 0.7619 | 0.4923 | 0.7674 |
| 时序+跨模态简化版 | 0.8056 | 0.5000 | 0.5238 | 0.5116 | 0.8227 |
| 完整模型（原始） | 0.8241 | 0.5385 | 0.6667 | 0.5957 | 0.8024 |

### 认知症状

| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
| ---- | ------ | ------ | ------ | ------ | --- |
| 仅时序增强 | 0.7130 | 0.4878 | 0.6667 | 0.5634 | 0.7654 |
| 仅跨模态一致性 | 0.7593 | 0.5476 | 0.7667 | 0.6389 | 0.8128 |
| 无自适应权重 | 0.6574 | 0.4286 | 0.7000 | 0.5316 | 0.7449 |
| 无分层分类 | 0.5926 | 0.3906 | 0.8333 | 0.5319 | 0.6423 |
| 时序+跨模态简化版 | 0.5926 | 0.3939 | 0.8667 | 0.5417 | 0.6782 |
| 完整模型（原始） | 0.8241 | 0.7200 | 0.6000 | 0.6545 | 0.8043 |

### 行为症状

| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
| ---- | ------ | ------ | ------ | ------ | --- |
| 仅时序增强 | 0.8333 | 0.5000 | 0.5556 | 0.5263 | 0.7222 |
| 仅跨模态一致性 | 0.9259 | 1.0000 | 0.5556 | 0.7143 | 0.8654 |
| 无自适应权重 | 0.5648 | 0.2542 | 0.8333 | 0.3896 | 0.7414 |
| 无分层分类 | 0.6852 | 0.3095 | 0.7222 | 0.4333 | 0.7364 |
| 时序+跨模态简化版 | 0.7037 | 0.3158 | 0.6667 | 0.4286 | 0.7944 |
| 完整模型（原始） | 0.8704 | 0.6111 | 0.6111 | 0.6111 | 0.8210 |

## 结论

1. **最佳整体性能**:
   - 仅跨模态一致性模型在总体性能上表现最好，F1分数为0.6726。
   - 原始完整模型的性能（F1=0.6529）也非常好，接近最佳模型。

2. **各模块贡献**:
   - **跨模态一致性增强**: 对模型性能有显著提升，特别是在精确率方面。
   - **时序增强**: 单独使用时对某些症状类型（如情绪症状）有提升，但整体效果不如跨模态一致性。
   - **自适应权重**: 对模型性能有一定贡献，移除后F1分数降至0.4994。
   - **分层分类**: 对模型性能有积极影响，特别是在提高精确率方面。

3. **症状类型差异**:
   - **情绪症状**: 原始完整模型表现最好（F1=0.7500），其次是仅时序增强模型（F1=0.7143）。
   - **躯体症状**: 仅跨模态一致性模型表现最好（F1=0.6522），其次是原始完整模型（F1=0.5957）。
   - **认知症状**: 仅跨模态一致性模型表现最好（F1=0.6389），其次是原始完整模型（F1=0.6545）。
   - **行为症状**: 仅跨模态一致性模型表现最好（F1=0.7143），其次是原始完整模型（F1=0.6111）。

4. **模型复杂性与性能权衡**:
   - 跨模态一致性单独使用时效果很好，表明这是一个有价值的特性。
   - 时序+跨模态简化版（不使用自适应权重和分层分类）的性能（F1=0.5111）低于原始完整模型（F1=0.6529），表明自适应权重和分层分类在结合时序和跨模态一致性时有一定贡献。

5. **建议**:
   - 对于实际应用，可以考虑使用原始完整模型或仅跨模态一致性模型，它们提供了最佳的整体性能。
   - 对于特定症状类型的检测，可以考虑使用针对该症状表现最好的模型配置。
   - 进一步研究可以探索如何更有效地结合这些特性，以避免模型中可能出现的过拟合问题。
   - 考虑简化模型架构，减少参数数量，可能会获得更好的泛化性能。

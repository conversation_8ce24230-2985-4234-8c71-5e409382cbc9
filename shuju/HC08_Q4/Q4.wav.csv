@relation openSMILE_features

@attribute name string
@attribute F0semitoneFrom27.5Hz_sma3nz_amean numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevNorm numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile20.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile50.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile80.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_pctlrange0-2 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanFallingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevFallingSlope numeric
@attribute loudness_sma3_amean numeric
@attribute loudness_sma3_stddevNorm numeric
@attribute loudness_sma3_percentile20.0 numeric
@attribute loudness_sma3_percentile50.0 numeric
@attribute loudness_sma3_percentile80.0 numeric
@attribute loudness_sma3_pctlrange0-2 numeric
@attribute loudness_sma3_meanRisingSlope numeric
@attribute loudness_sma3_stddevRisingSlope numeric
@attribute loudness_sma3_meanFallingSlope numeric
@attribute loudness_sma3_stddevFallingSlope numeric
@attribute spectralFlux_sma3_amean numeric
@attribute spectralFlux_sma3_stddevNorm numeric
@attribute mfcc1_sma3_amean numeric
@attribute mfcc1_sma3_stddevNorm numeric
@attribute mfcc2_sma3_amean numeric
@attribute mfcc2_sma3_stddevNorm numeric
@attribute mfcc3_sma3_amean numeric
@attribute mfcc3_sma3_stddevNorm numeric
@attribute mfcc4_sma3_amean numeric
@attribute mfcc4_sma3_stddevNorm numeric
@attribute jitterLocal_sma3nz_amean numeric
@attribute jitterLocal_sma3nz_stddevNorm numeric
@attribute shimmerLocaldB_sma3nz_amean numeric
@attribute shimmerLocaldB_sma3nz_stddevNorm numeric
@attribute HNRdBACF_sma3nz_amean numeric
@attribute HNRdBACF_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-H2_sma3nz_amean numeric
@attribute logRelF0-H1-H2_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-A3_sma3nz_amean numeric
@attribute logRelF0-H1-A3_sma3nz_stddevNorm numeric
@attribute F1frequency_sma3nz_amean numeric
@attribute F1frequency_sma3nz_stddevNorm numeric
@attribute F1bandwidth_sma3nz_amean numeric
@attribute F1bandwidth_sma3nz_stddevNorm numeric
@attribute F1amplitudeLogRelF0_sma3nz_amean numeric
@attribute F1amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F2frequency_sma3nz_amean numeric
@attribute F2frequency_sma3nz_stddevNorm numeric
@attribute F2bandwidth_sma3nz_amean numeric
@attribute F2bandwidth_sma3nz_stddevNorm numeric
@attribute F2amplitudeLogRelF0_sma3nz_amean numeric
@attribute F2amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F3frequency_sma3nz_amean numeric
@attribute F3frequency_sma3nz_stddevNorm numeric
@attribute F3bandwidth_sma3nz_amean numeric
@attribute F3bandwidth_sma3nz_stddevNorm numeric
@attribute F3amplitudeLogRelF0_sma3nz_amean numeric
@attribute F3amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute alphaRatioV_sma3nz_amean numeric
@attribute alphaRatioV_sma3nz_stddevNorm numeric
@attribute hammarbergIndexV_sma3nz_amean numeric
@attribute hammarbergIndexV_sma3nz_stddevNorm numeric
@attribute slopeV0-500_sma3nz_amean numeric
@attribute slopeV0-500_sma3nz_stddevNorm numeric
@attribute slopeV500-1500_sma3nz_amean numeric
@attribute slopeV500-1500_sma3nz_stddevNorm numeric
@attribute spectralFluxV_sma3nz_amean numeric
@attribute spectralFluxV_sma3nz_stddevNorm numeric
@attribute mfcc1V_sma3nz_amean numeric
@attribute mfcc1V_sma3nz_stddevNorm numeric
@attribute mfcc2V_sma3nz_amean numeric
@attribute mfcc2V_sma3nz_stddevNorm numeric
@attribute mfcc3V_sma3nz_amean numeric
@attribute mfcc3V_sma3nz_stddevNorm numeric
@attribute mfcc4V_sma3nz_amean numeric
@attribute mfcc4V_sma3nz_stddevNorm numeric
@attribute alphaRatioUV_sma3nz_amean numeric
@attribute hammarbergIndexUV_sma3nz_amean numeric
@attribute slopeUV0-500_sma3nz_amean numeric
@attribute slopeUV500-1500_sma3nz_amean numeric
@attribute spectralFluxUV_sma3nz_amean numeric
@attribute loudnessPeaksPerSec numeric
@attribute VoicedSegmentsPerSec numeric
@attribute MeanVoicedSegmentLengthSec numeric
@attribute StddevVoicedSegmentLengthSec numeric
@attribute MeanUnvoicedSegmentLength numeric
@attribute StddevUnvoicedSegmentLength numeric
@attribute equivalentSoundLevel_dBp numeric
@attribute class numeric

@data

'unknown',3.601254e+01,1.617578e-01,3.531319e+01,3.637760e+01,3.798257e+01,2.669384e+00,4.740894e+02,6.596219e+02,7.258411e+01,5.342014e+01,2.874078e-01,6.315493e-01,7.681913e-02,2.897097e-01,4.435332e-01,3.667141e-01,3.529285e+00,2.182923e+00,2.403409e+00,1.344781e+00,9.507772e-02,9.454011e-01,2.052036e+01,7.498949e-01,1.436669e+01,7.711871e-01,6.328197e+00,2.268042e+00,-1.430660e+01,-1.366827e+00,3.599240e-02,2.595393e+00,8.552052e-01,9.805436e-01,8.930622e+00,6.321006e-01,3.837134e+00,3.098372e+00,2.426406e+01,5.545388e-01,6.159537e+02,3.432242e-01,1.186350e+03,2.363577e-01,-8.377943e+01,-1.082970e+00,1.716878e+03,2.231635e-01,8.420817e+02,4.514675e-01,-8.568859e+01,-9.454715e-01,2.906732e+03,9.073911e-02,6.041254e+02,6.885185e-01,-8.863132e+01,-8.810955e-01,-1.751785e+01,-6.180862e-01,2.699127e+01,4.316423e-01,6.452082e-02,4.466871e-01,-2.605516e-02,-4.833214e-01,1.252672e-01,7.317534e-01,2.632658e+01,5.420887e-01,1.534783e+01,7.644716e-01,4.159375e+00,3.868920e+00,-2.251155e+01,-7.896242e-01,-1.009129e+01,1.827343e+01,-1.342418e-02,-6.891067e-03,3.843581e-02,3.375121e+00,1.981634e+00,3.229268e-01,2.265030e-01,1.731579e-01,2.650830e-01,-3.957767e+01,?

@relation openSMILE_features

@attribute name string
@attribute F0semitoneFrom27.5Hz_sma3nz_amean numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevNorm numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile20.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile50.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile80.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_pctlrange0-2 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanFallingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevFallingSlope numeric
@attribute loudness_sma3_amean numeric
@attribute loudness_sma3_stddevNorm numeric
@attribute loudness_sma3_percentile20.0 numeric
@attribute loudness_sma3_percentile50.0 numeric
@attribute loudness_sma3_percentile80.0 numeric
@attribute loudness_sma3_pctlrange0-2 numeric
@attribute loudness_sma3_meanRisingSlope numeric
@attribute loudness_sma3_stddevRisingSlope numeric
@attribute loudness_sma3_meanFallingSlope numeric
@attribute loudness_sma3_stddevFallingSlope numeric
@attribute spectralFlux_sma3_amean numeric
@attribute spectralFlux_sma3_stddevNorm numeric
@attribute mfcc1_sma3_amean numeric
@attribute mfcc1_sma3_stddevNorm numeric
@attribute mfcc2_sma3_amean numeric
@attribute mfcc2_sma3_stddevNorm numeric
@attribute mfcc3_sma3_amean numeric
@attribute mfcc3_sma3_stddevNorm numeric
@attribute mfcc4_sma3_amean numeric
@attribute mfcc4_sma3_stddevNorm numeric
@attribute jitterLocal_sma3nz_amean numeric
@attribute jitterLocal_sma3nz_stddevNorm numeric
@attribute shimmerLocaldB_sma3nz_amean numeric
@attribute shimmerLocaldB_sma3nz_stddevNorm numeric
@attribute HNRdBACF_sma3nz_amean numeric
@attribute HNRdBACF_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-H2_sma3nz_amean numeric
@attribute logRelF0-H1-H2_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-A3_sma3nz_amean numeric
@attribute logRelF0-H1-A3_sma3nz_stddevNorm numeric
@attribute F1frequency_sma3nz_amean numeric
@attribute F1frequency_sma3nz_stddevNorm numeric
@attribute F1bandwidth_sma3nz_amean numeric
@attribute F1bandwidth_sma3nz_stddevNorm numeric
@attribute F1amplitudeLogRelF0_sma3nz_amean numeric
@attribute F1amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F2frequency_sma3nz_amean numeric
@attribute F2frequency_sma3nz_stddevNorm numeric
@attribute F2bandwidth_sma3nz_amean numeric
@attribute F2bandwidth_sma3nz_stddevNorm numeric
@attribute F2amplitudeLogRelF0_sma3nz_amean numeric
@attribute F2amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F3frequency_sma3nz_amean numeric
@attribute F3frequency_sma3nz_stddevNorm numeric
@attribute F3bandwidth_sma3nz_amean numeric
@attribute F3bandwidth_sma3nz_stddevNorm numeric
@attribute F3amplitudeLogRelF0_sma3nz_amean numeric
@attribute F3amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute alphaRatioV_sma3nz_amean numeric
@attribute alphaRatioV_sma3nz_stddevNorm numeric
@attribute hammarbergIndexV_sma3nz_amean numeric
@attribute hammarbergIndexV_sma3nz_stddevNorm numeric
@attribute slopeV0-500_sma3nz_amean numeric
@attribute slopeV0-500_sma3nz_stddevNorm numeric
@attribute slopeV500-1500_sma3nz_amean numeric
@attribute slopeV500-1500_sma3nz_stddevNorm numeric
@attribute spectralFluxV_sma3nz_amean numeric
@attribute spectralFluxV_sma3nz_stddevNorm numeric
@attribute mfcc1V_sma3nz_amean numeric
@attribute mfcc1V_sma3nz_stddevNorm numeric
@attribute mfcc2V_sma3nz_amean numeric
@attribute mfcc2V_sma3nz_stddevNorm numeric
@attribute mfcc3V_sma3nz_amean numeric
@attribute mfcc3V_sma3nz_stddevNorm numeric
@attribute mfcc4V_sma3nz_amean numeric
@attribute mfcc4V_sma3nz_stddevNorm numeric
@attribute alphaRatioUV_sma3nz_amean numeric
@attribute hammarbergIndexUV_sma3nz_amean numeric
@attribute slopeUV0-500_sma3nz_amean numeric
@attribute slopeUV500-1500_sma3nz_amean numeric
@attribute spectralFluxUV_sma3nz_amean numeric
@attribute loudnessPeaksPerSec numeric
@attribute VoicedSegmentsPerSec numeric
@attribute MeanVoicedSegmentLengthSec numeric
@attribute StddevVoicedSegmentLengthSec numeric
@attribute MeanUnvoicedSegmentLength numeric
@attribute StddevUnvoicedSegmentLength numeric
@attribute equivalentSoundLevel_dBp numeric
@attribute class numeric

@data

'unknown',3.337186e+01,1.857738e-01,3.041661e+01,3.425514e+01,3.765993e+01,7.243317e+00,3.063066e+02,4.154746e+02,1.469546e+02,2.623576e+02,4.544827e-01,7.667015e-01,1.268984e-01,3.923430e-01,7.210054e-01,5.941070e-01,7.025717e+00,4.599020e+00,6.481812e+00,4.296221e+00,2.092472e-01,1.112785e+00,1.537678e+01,9.173248e-01,6.436916e+00,2.152423e+00,8.940077e+00,1.391125e+00,-1.871168e+01,-1.022282e+00,4.836118e-02,1.829417e+00,1.248566e+00,8.484716e-01,4.886043e+00,1.133211e+00,2.901663e+00,4.783824e+00,2.033822e+01,6.564000e-01,5.307629e+02,4.398858e-01,1.108064e+03,2.379262e-01,-9.971638e+01,-9.484619e-01,1.641777e+03,1.993897e-01,8.280085e+02,5.018381e-01,-9.337801e+01,-9.265000e-01,2.774579e+03,1.239742e-01,8.043892e+02,6.984927e-01,-9.611650e+01,-8.731841e-01,-1.244185e+01,-6.242092e-01,1.994154e+01,4.340183e-01,7.683279e-02,4.422276e-01,-2.451166e-02,-5.005405e-01,3.036757e-01,8.308166e-01,2.177784e+01,5.399569e-01,5.634503e+00,2.569191e+00,8.762142e+00,1.653764e+00,-2.780910e+01,-6.285121e-01,-4.989990e+00,1.214885e+01,9.867788e-04,-7.041413e-03,7.562983e-02,3.178603e+00,2.501454e+00,2.241473e-01,1.856631e-01,1.704741e-01,2.214378e-01,-3.556698e+01,?

@relation openSMILE_features

@attribute name string
@attribute F0semitoneFrom27.5Hz_sma3nz_amean numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevNorm numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile20.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile50.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile80.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_pctlrange0-2 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanFallingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevFallingSlope numeric
@attribute loudness_sma3_amean numeric
@attribute loudness_sma3_stddevNorm numeric
@attribute loudness_sma3_percentile20.0 numeric
@attribute loudness_sma3_percentile50.0 numeric
@attribute loudness_sma3_percentile80.0 numeric
@attribute loudness_sma3_pctlrange0-2 numeric
@attribute loudness_sma3_meanRisingSlope numeric
@attribute loudness_sma3_stddevRisingSlope numeric
@attribute loudness_sma3_meanFallingSlope numeric
@attribute loudness_sma3_stddevFallingSlope numeric
@attribute spectralFlux_sma3_amean numeric
@attribute spectralFlux_sma3_stddevNorm numeric
@attribute mfcc1_sma3_amean numeric
@attribute mfcc1_sma3_stddevNorm numeric
@attribute mfcc2_sma3_amean numeric
@attribute mfcc2_sma3_stddevNorm numeric
@attribute mfcc3_sma3_amean numeric
@attribute mfcc3_sma3_stddevNorm numeric
@attribute mfcc4_sma3_amean numeric
@attribute mfcc4_sma3_stddevNorm numeric
@attribute jitterLocal_sma3nz_amean numeric
@attribute jitterLocal_sma3nz_stddevNorm numeric
@attribute shimmerLocaldB_sma3nz_amean numeric
@attribute shimmerLocaldB_sma3nz_stddevNorm numeric
@attribute HNRdBACF_sma3nz_amean numeric
@attribute HNRdBACF_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-H2_sma3nz_amean numeric
@attribute logRelF0-H1-H2_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-A3_sma3nz_amean numeric
@attribute logRelF0-H1-A3_sma3nz_stddevNorm numeric
@attribute F1frequency_sma3nz_amean numeric
@attribute F1frequency_sma3nz_stddevNorm numeric
@attribute F1bandwidth_sma3nz_amean numeric
@attribute F1bandwidth_sma3nz_stddevNorm numeric
@attribute F1amplitudeLogRelF0_sma3nz_amean numeric
@attribute F1amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F2frequency_sma3nz_amean numeric
@attribute F2frequency_sma3nz_stddevNorm numeric
@attribute F2bandwidth_sma3nz_amean numeric
@attribute F2bandwidth_sma3nz_stddevNorm numeric
@attribute F2amplitudeLogRelF0_sma3nz_amean numeric
@attribute F2amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F3frequency_sma3nz_amean numeric
@attribute F3frequency_sma3nz_stddevNorm numeric
@attribute F3bandwidth_sma3nz_amean numeric
@attribute F3bandwidth_sma3nz_stddevNorm numeric
@attribute F3amplitudeLogRelF0_sma3nz_amean numeric
@attribute F3amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute alphaRatioV_sma3nz_amean numeric
@attribute alphaRatioV_sma3nz_stddevNorm numeric
@attribute hammarbergIndexV_sma3nz_amean numeric
@attribute hammarbergIndexV_sma3nz_stddevNorm numeric
@attribute slopeV0-500_sma3nz_amean numeric
@attribute slopeV0-500_sma3nz_stddevNorm numeric
@attribute slopeV500-1500_sma3nz_amean numeric
@attribute slopeV500-1500_sma3nz_stddevNorm numeric
@attribute spectralFluxV_sma3nz_amean numeric
@attribute spectralFluxV_sma3nz_stddevNorm numeric
@attribute mfcc1V_sma3nz_amean numeric
@attribute mfcc1V_sma3nz_stddevNorm numeric
@attribute mfcc2V_sma3nz_amean numeric
@attribute mfcc2V_sma3nz_stddevNorm numeric
@attribute mfcc3V_sma3nz_amean numeric
@attribute mfcc3V_sma3nz_stddevNorm numeric
@attribute mfcc4V_sma3nz_amean numeric
@attribute mfcc4V_sma3nz_stddevNorm numeric
@attribute alphaRatioUV_sma3nz_amean numeric
@attribute hammarbergIndexUV_sma3nz_amean numeric
@attribute slopeUV0-500_sma3nz_amean numeric
@attribute slopeUV500-1500_sma3nz_amean numeric
@attribute spectralFluxUV_sma3nz_amean numeric
@attribute loudnessPeaksPerSec numeric
@attribute VoicedSegmentsPerSec numeric
@attribute MeanVoicedSegmentLengthSec numeric
@attribute StddevVoicedSegmentLengthSec numeric
@attribute MeanUnvoicedSegmentLength numeric
@attribute StddevUnvoicedSegmentLength numeric
@attribute equivalentSoundLevel_dBp numeric
@attribute class numeric

@data

'unknown',3.269680e+01,2.425140e-01,2.428362e+01,3.542387e+01,3.804051e+01,1.375689e+01,4.272215e+02,6.986276e+02,1.758509e+02,3.274646e+02,4.081233e-01,6.280051e-01,1.655148e-01,3.849774e-01,6.243092e-01,4.587945e-01,5.560493e+00,3.633339e+00,4.204469e+00,2.602022e+00,1.447449e-01,8.627254e-01,1.915998e+01,9.000376e-01,6.255117e+00,2.068690e+00,8.814862e-01,1.849512e+01,-8.979783e+00,-1.757505e+00,4.704152e-02,1.834220e+00,1.123815e+00,9.094160e-01,6.155905e+00,1.039491e+00,3.297863e+00,4.026860e+00,2.364347e+01,5.935009e-01,5.158326e+02,4.314670e-01,1.196812e+03,2.574281e-01,-8.932031e+01,-1.070403e+00,1.523180e+03,2.080892e-01,8.763549e+02,4.102943e-01,-8.503185e+01,-1.020955e+00,2.643120e+03,1.109337e-01,8.050466e+02,6.125900e-01,-9.058675e+01,-9.096605e-01,-1.399550e+01,-6.730938e-01,2.453110e+01,4.458320e-01,7.727956e-02,3.878173e-01,-2.272318e-02,-6.106373e-01,1.929729e-01,6.557441e-01,2.630916e+01,5.709389e-01,7.389380e+00,1.611436e+00,-2.880577e+00,-6.213682e+00,-1.362612e+01,-1.202112e+00,-4.634372e+00,1.343770e+01,5.472160e-03,-5.642553e-03,6.534532e-02,3.680485e+00,2.469636e+00,2.424590e-01,1.822281e-01,1.622642e-01,1.939621e-01,-3.712033e+01,?

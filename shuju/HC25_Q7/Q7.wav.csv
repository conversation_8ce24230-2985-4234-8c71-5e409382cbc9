@relation openSMILE_features

@attribute name string
@attribute F0semitoneFrom27.5Hz_sma3nz_amean numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevNorm numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile20.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile50.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile80.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_pctlrange0-2 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanFallingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevFallingSlope numeric
@attribute loudness_sma3_amean numeric
@attribute loudness_sma3_stddevNorm numeric
@attribute loudness_sma3_percentile20.0 numeric
@attribute loudness_sma3_percentile50.0 numeric
@attribute loudness_sma3_percentile80.0 numeric
@attribute loudness_sma3_pctlrange0-2 numeric
@attribute loudness_sma3_meanRisingSlope numeric
@attribute loudness_sma3_stddevRisingSlope numeric
@attribute loudness_sma3_meanFallingSlope numeric
@attribute loudness_sma3_stddevFallingSlope numeric
@attribute spectralFlux_sma3_amean numeric
@attribute spectralFlux_sma3_stddevNorm numeric
@attribute mfcc1_sma3_amean numeric
@attribute mfcc1_sma3_stddevNorm numeric
@attribute mfcc2_sma3_amean numeric
@attribute mfcc2_sma3_stddevNorm numeric
@attribute mfcc3_sma3_amean numeric
@attribute mfcc3_sma3_stddevNorm numeric
@attribute mfcc4_sma3_amean numeric
@attribute mfcc4_sma3_stddevNorm numeric
@attribute jitterLocal_sma3nz_amean numeric
@attribute jitterLocal_sma3nz_stddevNorm numeric
@attribute shimmerLocaldB_sma3nz_amean numeric
@attribute shimmerLocaldB_sma3nz_stddevNorm numeric
@attribute HNRdBACF_sma3nz_amean numeric
@attribute HNRdBACF_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-H2_sma3nz_amean numeric
@attribute logRelF0-H1-H2_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-A3_sma3nz_amean numeric
@attribute logRelF0-H1-A3_sma3nz_stddevNorm numeric
@attribute F1frequency_sma3nz_amean numeric
@attribute F1frequency_sma3nz_stddevNorm numeric
@attribute F1bandwidth_sma3nz_amean numeric
@attribute F1bandwidth_sma3nz_stddevNorm numeric
@attribute F1amplitudeLogRelF0_sma3nz_amean numeric
@attribute F1amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F2frequency_sma3nz_amean numeric
@attribute F2frequency_sma3nz_stddevNorm numeric
@attribute F2bandwidth_sma3nz_amean numeric
@attribute F2bandwidth_sma3nz_stddevNorm numeric
@attribute F2amplitudeLogRelF0_sma3nz_amean numeric
@attribute F2amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F3frequency_sma3nz_amean numeric
@attribute F3frequency_sma3nz_stddevNorm numeric
@attribute F3bandwidth_sma3nz_amean numeric
@attribute F3bandwidth_sma3nz_stddevNorm numeric
@attribute F3amplitudeLogRelF0_sma3nz_amean numeric
@attribute F3amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute alphaRatioV_sma3nz_amean numeric
@attribute alphaRatioV_sma3nz_stddevNorm numeric
@attribute hammarbergIndexV_sma3nz_amean numeric
@attribute hammarbergIndexV_sma3nz_stddevNorm numeric
@attribute slopeV0-500_sma3nz_amean numeric
@attribute slopeV0-500_sma3nz_stddevNorm numeric
@attribute slopeV500-1500_sma3nz_amean numeric
@attribute slopeV500-1500_sma3nz_stddevNorm numeric
@attribute spectralFluxV_sma3nz_amean numeric
@attribute spectralFluxV_sma3nz_stddevNorm numeric
@attribute mfcc1V_sma3nz_amean numeric
@attribute mfcc1V_sma3nz_stddevNorm numeric
@attribute mfcc2V_sma3nz_amean numeric
@attribute mfcc2V_sma3nz_stddevNorm numeric
@attribute mfcc3V_sma3nz_amean numeric
@attribute mfcc3V_sma3nz_stddevNorm numeric
@attribute mfcc4V_sma3nz_amean numeric
@attribute mfcc4V_sma3nz_stddevNorm numeric
@attribute alphaRatioUV_sma3nz_amean numeric
@attribute hammarbergIndexUV_sma3nz_amean numeric
@attribute slopeUV0-500_sma3nz_amean numeric
@attribute slopeUV500-1500_sma3nz_amean numeric
@attribute spectralFluxUV_sma3nz_amean numeric
@attribute loudnessPeaksPerSec numeric
@attribute VoicedSegmentsPerSec numeric
@attribute MeanVoicedSegmentLengthSec numeric
@attribute StddevVoicedSegmentLengthSec numeric
@attribute MeanUnvoicedSegmentLength numeric
@attribute StddevUnvoicedSegmentLength numeric
@attribute equivalentSoundLevel_dBp numeric
@attribute class numeric

@data

'unknown',3.247481e+01,2.391160e-01,2.398930e+01,3.516673e+01,3.745105e+01,1.346175e+01,4.442533e+02,6.252083e+02,1.298079e+02,2.184532e+02,3.871643e-01,6.234646e-01,1.538479e-01,3.665242e-01,6.059423e-01,4.520944e-01,5.185881e+00,3.146251e+00,4.176538e+00,2.598455e+00,1.313411e-01,8.675082e-01,1.969142e+01,8.491110e-01,6.692920e+00,1.916783e+00,2.649712e+00,6.230619e+00,-8.214621e+00,-1.834032e+00,4.995671e-02,1.884147e+00,1.148677e+00,9.444103e-01,6.123374e+00,1.057895e+00,2.919993e+00,4.578104e+00,2.244955e+01,5.994144e-01,5.053837e+02,4.332231e-01,1.160106e+03,2.513994e-01,-8.318740e+01,-1.124817e+00,1.517239e+03,2.094403e-01,8.702999e+02,4.169586e-01,-8.062778e+01,-1.052820e+00,2.618807e+03,1.206473e-01,7.449811e+02,5.505105e-01,-8.593845e+01,-9.419943e-01,-1.347712e+01,-6.795023e-01,2.368699e+01,4.419006e-01,7.179785e-02,4.496980e-01,-2.108101e-02,-6.328936e-01,1.704938e-01,6.710708e-01,2.605154e+01,5.598425e-01,7.231901e+00,1.646008e+00,-6.397737e-01,-2.852970e+01,-1.197278e+01,-1.291366e+00,-5.447037e+00,1.425259e+01,1.088676e-03,-5.228265e-03,5.953050e-02,3.603493e+00,2.436623e+00,2.564647e-01,1.921137e-01,1.483051e-01,2.093154e-01,-3.805751e+01,?

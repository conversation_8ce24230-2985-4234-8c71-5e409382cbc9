@relation openSMILE_features

@attribute name string
@attribute F0semitoneFrom27.5Hz_sma3nz_amean numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevNorm numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile20.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile50.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_percentile80.0 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_pctlrange0-2 numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevRisingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_meanFallingSlope numeric
@attribute F0semitoneFrom27.5Hz_sma3nz_stddevFallingSlope numeric
@attribute loudness_sma3_amean numeric
@attribute loudness_sma3_stddevNorm numeric
@attribute loudness_sma3_percentile20.0 numeric
@attribute loudness_sma3_percentile50.0 numeric
@attribute loudness_sma3_percentile80.0 numeric
@attribute loudness_sma3_pctlrange0-2 numeric
@attribute loudness_sma3_meanRisingSlope numeric
@attribute loudness_sma3_stddevRisingSlope numeric
@attribute loudness_sma3_meanFallingSlope numeric
@attribute loudness_sma3_stddevFallingSlope numeric
@attribute spectralFlux_sma3_amean numeric
@attribute spectralFlux_sma3_stddevNorm numeric
@attribute mfcc1_sma3_amean numeric
@attribute mfcc1_sma3_stddevNorm numeric
@attribute mfcc2_sma3_amean numeric
@attribute mfcc2_sma3_stddevNorm numeric
@attribute mfcc3_sma3_amean numeric
@attribute mfcc3_sma3_stddevNorm numeric
@attribute mfcc4_sma3_amean numeric
@attribute mfcc4_sma3_stddevNorm numeric
@attribute jitterLocal_sma3nz_amean numeric
@attribute jitterLocal_sma3nz_stddevNorm numeric
@attribute shimmerLocaldB_sma3nz_amean numeric
@attribute shimmerLocaldB_sma3nz_stddevNorm numeric
@attribute HNRdBACF_sma3nz_amean numeric
@attribute HNRdBACF_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-H2_sma3nz_amean numeric
@attribute logRelF0-H1-H2_sma3nz_stddevNorm numeric
@attribute logRelF0-H1-A3_sma3nz_amean numeric
@attribute logRelF0-H1-A3_sma3nz_stddevNorm numeric
@attribute F1frequency_sma3nz_amean numeric
@attribute F1frequency_sma3nz_stddevNorm numeric
@attribute F1bandwidth_sma3nz_amean numeric
@attribute F1bandwidth_sma3nz_stddevNorm numeric
@attribute F1amplitudeLogRelF0_sma3nz_amean numeric
@attribute F1amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F2frequency_sma3nz_amean numeric
@attribute F2frequency_sma3nz_stddevNorm numeric
@attribute F2bandwidth_sma3nz_amean numeric
@attribute F2bandwidth_sma3nz_stddevNorm numeric
@attribute F2amplitudeLogRelF0_sma3nz_amean numeric
@attribute F2amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute F3frequency_sma3nz_amean numeric
@attribute F3frequency_sma3nz_stddevNorm numeric
@attribute F3bandwidth_sma3nz_amean numeric
@attribute F3bandwidth_sma3nz_stddevNorm numeric
@attribute F3amplitudeLogRelF0_sma3nz_amean numeric
@attribute F3amplitudeLogRelF0_sma3nz_stddevNorm numeric
@attribute alphaRatioV_sma3nz_amean numeric
@attribute alphaRatioV_sma3nz_stddevNorm numeric
@attribute hammarbergIndexV_sma3nz_amean numeric
@attribute hammarbergIndexV_sma3nz_stddevNorm numeric
@attribute slopeV0-500_sma3nz_amean numeric
@attribute slopeV0-500_sma3nz_stddevNorm numeric
@attribute slopeV500-1500_sma3nz_amean numeric
@attribute slopeV500-1500_sma3nz_stddevNorm numeric
@attribute spectralFluxV_sma3nz_amean numeric
@attribute spectralFluxV_sma3nz_stddevNorm numeric
@attribute mfcc1V_sma3nz_amean numeric
@attribute mfcc1V_sma3nz_stddevNorm numeric
@attribute mfcc2V_sma3nz_amean numeric
@attribute mfcc2V_sma3nz_stddevNorm numeric
@attribute mfcc3V_sma3nz_amean numeric
@attribute mfcc3V_sma3nz_stddevNorm numeric
@attribute mfcc4V_sma3nz_amean numeric
@attribute mfcc4V_sma3nz_stddevNorm numeric
@attribute alphaRatioUV_sma3nz_amean numeric
@attribute hammarbergIndexUV_sma3nz_amean numeric
@attribute slopeUV0-500_sma3nz_amean numeric
@attribute slopeUV500-1500_sma3nz_amean numeric
@attribute spectralFluxUV_sma3nz_amean numeric
@attribute loudnessPeaksPerSec numeric
@attribute VoicedSegmentsPerSec numeric
@attribute MeanVoicedSegmentLengthSec numeric
@attribute StddevVoicedSegmentLengthSec numeric
@attribute MeanUnvoicedSegmentLength numeric
@attribute StddevUnvoicedSegmentLength numeric
@attribute equivalentSoundLevel_dBp numeric
@attribute class numeric

@data

'unknown',2.819768e+01,2.439335e-01,2.042194e+01,3.060217e+01,3.395691e+01,1.353497e+01,2.914702e+02,3.819993e+02,9.290553e+01,1.426681e+02,2.711499e-01,7.969577e-01,7.965647e-02,2.117971e-01,4.332660e-01,3.536095e-01,3.844936e+00,2.862113e+00,2.874097e+00,2.079227e+00,9.338602e-02,1.082298e+00,2.541064e+01,5.188457e-01,1.143423e+01,1.033218e+00,8.559482e+00,1.687792e+00,-9.105724e+00,-1.960539e+00,3.815375e-02,2.198679e+00,1.080015e+00,9.495971e-01,5.778356e+00,9.391052e-01,4.233337e+00,2.438735e+00,2.748460e+01,3.750358e-01,5.346072e+02,3.853486e-01,1.209245e+03,2.005706e-01,-7.530875e+01,-1.201539e+00,1.561626e+03,2.186594e-01,9.417632e+02,4.328874e-01,-8.190543e+01,-1.007126e+00,2.812302e+03,1.257057e-01,7.089064e+02,8.539116e-01,-8.763996e+01,-8.922622e-01,-1.629621e+01,-5.251735e-01,2.734350e+01,3.437655e-01,4.417771e-02,1.060954e+00,-1.496369e-02,-9.989953e-01,1.260003e-01,8.525469e-01,3.128584e+01,3.326932e-01,1.320765e+01,9.239508e-01,1.053531e+01,1.489672e+00,-1.507522e+01,-1.176468e+00,-9.010716e+00,2.111820e+01,-1.937423e-02,4.076558e-03,2.968574e-02,2.162434e+00,1.888218e+00,3.408000e-01,2.838298e-01,1.766197e-01,2.174173e-01,-3.883617e+01,?

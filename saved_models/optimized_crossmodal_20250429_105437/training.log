2025-04-29 10:54:39,818 - __main__ - INFO - 训练参数:
2025-04-29 10:54:39,818 - __main__ - INFO - audio_dim: 128
2025-04-29 10:54:39,818 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 10:54:39,818 - __main__ - INFO - batch_size: 4
2025-04-29 10:54:39,818 - __main__ - INFO - device: cpu
2025-04-29 10:54:39,818 - __main__ - INFO - dropout: 0.3
2025-04-29 10:54:39,818 - __main__ - INFO - early_stop: 10
2025-04-29 10:54:39,818 - __main__ - INFO - epochs: 10
2025-04-29 10:54:39,818 - __main__ - INFO - experiment_name: optimized_crossmodal_20250429_105437
2025-04-29 10:54:39,818 - __main__ - INFO - factor: 0.5
2025-04-29 10:54:39,818 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 10:54:39,818 - __main__ - INFO - hidden_dim: 256
2025-04-29 10:54:39,818 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 10:54:39,818 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 10:54:39,818 - __main__ - INFO - lr: 0.0003
2025-04-29 10:54:39,818 - __main__ - INFO - mask_prob: 0.15
2025-04-29 10:54:39,818 - __main__ - INFO - mask_ratio: 0.2
2025-04-29 10:54:39,818 - __main__ - INFO - min_lr: 1e-06
2025-04-29 10:54:39,818 - __main__ - INFO - noise_level: 0.1
2025-04-29 10:54:39,818 - __main__ - INFO - noise_prob: 0.2
2025-04-29 10:54:39,818 - __main__ - INFO - num_workers: 2
2025-04-29 10:54:39,818 - __main__ - INFO - patience: 5
2025-04-29 10:54:39,818 - __main__ - INFO - save_all_epochs: False
2025-04-29 10:54:39,818 - __main__ - INFO - save_dir: ./saved_models/optimized_crossmodal_20250429_105437
2025-04-29 10:54:39,818 - __main__ - INFO - scheduler: reduce
2025-04-29 10:54:39,818 - __main__ - INFO - seed: 42
2025-04-29 10:54:39,818 - __main__ - INFO - symptom_files: []
2025-04-29 10:54:39,818 - __main__ - INFO - text_dim: 768
2025-04-29 10:54:39,818 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 10:54:39,818 - __main__ - INFO - use_augmentation: True
2025-04-29 10:54:39,818 - __main__ - INFO - visual_dim: 1024
2025-04-29 10:54:39,818 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 10:54:39,818 - __main__ - INFO - visualize: True
2025-04-29 10:54:39,818 - __main__ - INFO - weight_decay: 1e-05
2025-04-29 10:54:39,818 - __main__ - INFO - 准备数据...
2025-04-29 10:54:39,877 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 10:54:51,466 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 10:54:51,466 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 10:54:51,467 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 10:54:51,467 - __main__ - INFO - 类别数量: 4
2025-04-29 10:54:51,467 - __main__ - INFO - 使用设备: cpu
2025-04-29 10:54:51,497 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 10:54:51,497 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 10:54:51,497 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 10:54:52,016 - __main__ - INFO - 开始训练...
2025-04-29 10:54:53,564 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,569 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,572 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,575 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,578 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,581 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,584 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,586 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,589 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,592 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,594 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,597 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,600 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,604 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,608 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,612 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,615 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,618 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,621 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,623 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,626 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,629 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,631 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,634 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,637 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,640 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,644 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,648 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,652 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,655 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,658 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,661 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,664 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,666 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,670 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,673 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,676 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,679 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,683 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,688 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,691 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,694 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,697 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,700 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,704 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,707 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,711 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,714 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,717 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,718 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:54:53,720 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,723 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,726 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,728 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,731 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,734 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,737 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,741 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,742 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:54:53,745 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,747 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,750 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,753 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,756 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,759 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:54:53,761 - __main__ - ERROR - 训练时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,273 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,340 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,342 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,346 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,348 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,351 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,353 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,356 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,358 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,360 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,362 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,364 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,366 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1
2025-04-29 10:55:05,369 - __main__ - ERROR - 验证时发生错误: all elements of input should be between 0 and 1

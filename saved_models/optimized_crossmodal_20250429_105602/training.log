2025-04-29 10:56:04,270 - __main__ - INFO - 训练参数:
2025-04-29 10:56:04,270 - __main__ - INFO - audio_dim: 128
2025-04-29 10:56:04,270 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 10:56:04,270 - __main__ - INFO - batch_size: 4
2025-04-29 10:56:04,270 - __main__ - INFO - device: cpu
2025-04-29 10:56:04,270 - __main__ - INFO - dropout: 0.3
2025-04-29 10:56:04,270 - __main__ - INFO - early_stop: 10
2025-04-29 10:56:04,270 - __main__ - INFO - epochs: 10
2025-04-29 10:56:04,270 - __main__ - INFO - experiment_name: optimized_crossmodal_20250429_105602
2025-04-29 10:56:04,270 - __main__ - INFO - factor: 0.5
2025-04-29 10:56:04,270 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 10:56:04,270 - __main__ - INFO - hidden_dim: 256
2025-04-29 10:56:04,270 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 10:56:04,270 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 10:56:04,270 - __main__ - INFO - lr: 0.0003
2025-04-29 10:56:04,270 - __main__ - INFO - mask_prob: 0.15
2025-04-29 10:56:04,270 - __main__ - INFO - mask_ratio: 0.2
2025-04-29 10:56:04,270 - __main__ - INFO - min_lr: 1e-06
2025-04-29 10:56:04,270 - __main__ - INFO - noise_level: 0.1
2025-04-29 10:56:04,270 - __main__ - INFO - noise_prob: 0.2
2025-04-29 10:56:04,270 - __main__ - INFO - num_workers: 2
2025-04-29 10:56:04,271 - __main__ - INFO - patience: 5
2025-04-29 10:56:04,271 - __main__ - INFO - save_all_epochs: False
2025-04-29 10:56:04,271 - __main__ - INFO - save_dir: ./saved_models/optimized_crossmodal_20250429_105602
2025-04-29 10:56:04,271 - __main__ - INFO - scheduler: reduce
2025-04-29 10:56:04,271 - __main__ - INFO - seed: 42
2025-04-29 10:56:04,271 - __main__ - INFO - symptom_files: []
2025-04-29 10:56:04,271 - __main__ - INFO - text_dim: 768
2025-04-29 10:56:04,271 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 10:56:04,271 - __main__ - INFO - use_augmentation: True
2025-04-29 10:56:04,271 - __main__ - INFO - visual_dim: 1024
2025-04-29 10:56:04,271 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 10:56:04,271 - __main__ - INFO - visualize: True
2025-04-29 10:56:04,271 - __main__ - INFO - weight_decay: 1e-05
2025-04-29 10:56:04,271 - __main__ - INFO - 准备数据...
2025-04-29 10:56:04,324 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 10:56:15,959 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 10:56:15,959 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 10:56:15,959 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 10:56:15,959 - __main__ - INFO - 类别数量: 4
2025-04-29 10:56:15,959 - __main__ - INFO - 使用设备: cpu
2025-04-29 10:56:15,980 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 10:56:15,981 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 10:56:15,981 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 10:56:15,981 - __main__ - INFO - 使用BCEWithLogitsLoss损失函数
2025-04-29 10:56:16,360 - __main__ - INFO - 开始训练...
2025-04-29 10:56:18,552 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:56:18,649 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:56:40,368 - __main__ - INFO - Epoch 1/10 - Train Loss: 0.8036, Train Acc: 0.6279, Train F1: 0.3165, Val Loss: 0.6852, Val Acc: 0.6830, Val F1: 0.3899, LR: 0.00030000
2025-04-29 10:56:40,466 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.6852)
2025-04-29 10:56:42,438 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:56:42,688 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:04,406 - __main__ - INFO - Epoch 2/10 - Train Loss: 0.7356, Train Acc: 0.6611, Train F1: 0.3450, Val Loss: 0.6332, Val Acc: 0.6875, Val F1: 0.3814, LR: 0.00030000
2025-04-29 10:57:04,443 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.6332)
2025-04-29 10:57:06,179 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:06,535 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:28,462 - __main__ - INFO - Epoch 3/10 - Train Loss: 0.7052, Train Acc: 0.6729, Train F1: 0.3552, Val Loss: 0.6130, Val Acc: 0.7009, Val F1: 0.4554, LR: 0.00030000
2025-04-29 10:57:28,499 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.6130)
2025-04-29 10:57:30,206 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:30,757 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:52,513 - __main__ - INFO - Epoch 4/10 - Train Loss: 0.6505, Train Acc: 0.7158, Train F1: 0.3863, Val Loss: 0.6491, Val Acc: 0.7143, Val F1: 0.4152, LR: 0.00030000
2025-04-29 10:57:52,514 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 10:57:54,322 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:57:54,540 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:58:16,496 - __main__ - INFO - Epoch 5/10 - Train Loss: 0.6258, Train Acc: 0.7373, Train F1: 0.3964, Val Loss: 0.5977, Val Acc: 0.7054, Val F1: 0.4173, LR: 0.00030000
2025-04-29 10:58:16,531 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.5977)
2025-04-29 10:58:18,637 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:58:18,824 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:58:40,604 - __main__ - INFO - Epoch 6/10 - Train Loss: 0.5946, Train Acc: 0.7422, Train F1: 0.4103, Val Loss: 0.5704, Val Acc: 0.7545, Val F1: 0.5491, LR: 0.00030000
2025-04-29 10:58:40,636 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.5704)
2025-04-29 10:58:42,544 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:58:42,971 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:59:04,619 - __main__ - INFO - Epoch 7/10 - Train Loss: 0.5427, Train Acc: 0.7695, Train F1: 0.4196, Val Loss: 0.5770, Val Acc: 0.7143, Val F1: 0.3735, LR: 0.00030000
2025-04-29 10:59:04,619 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 10:59:06,961 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:59:28,600 - __main__ - INFO - Epoch 8/10 - Train Loss: 0.5394, Train Acc: 0.7615, Train F1: 0.3878, Val Loss: 0.5109, Val Acc: 0.7411, Val F1: 0.3415, LR: 0.00030000
2025-04-29 10:59:28,638 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.5109)
2025-04-29 10:59:30,547 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:59:30,622 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:59:52,629 - __main__ - INFO - Epoch 9/10 - Train Loss: 0.5492, Train Acc: 0.7539, Train F1: 0.3119, Val Loss: 0.4966, Val Acc: 0.7768, Val F1: 0.5265, LR: 0.00030000
2025-04-29 10:59:52,656 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_105602/best_model.pth (验证损失: 0.4966)
2025-04-29 10:59:54,698 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 10:59:54,797 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:00:16,708 - __main__ - INFO - Epoch 10/10 - Train Loss: 0.5084, Train Acc: 0.7715, Train F1: 0.3601, Val Loss: 0.5364, Val Acc: 0.7589, Val F1: 0.5104, LR: 0.00030000
2025-04-29 11:00:16,708 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 11:00:17,041 - __main__ - INFO - 训练完成! 最佳验证损失: 0.4966 (Epoch 9)
2025-04-29 11:00:17,041 - __main__ - INFO - 在测试集上评估最佳模型...
2025-04-29 11:00:28,737 - __main__ - INFO - 测试集损失: 0.5322, 测试集准确率: 0.7974, 测试集F1: 0.5251
2025-04-29 11:00:28,738 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.77      0.56      0.65        18
           1       0.60      0.67      0.63         9
           2       0.67      0.36      0.47        22
           3       0.30      0.43      0.35         7

   micro avg       0.60      0.48      0.53        56
   macro avg       0.58      0.50      0.53        56
weighted avg       0.64      0.48      0.54        56
 samples avg       0.23      0.26      0.23        56

2025-04-29 11:00:28,746 - __main__ - INFO - 测试损失: 0.5322, 测试准确率: 0.7974, 测试F1分数: 0.5251
2025-04-29 11:00:28,746 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.77      0.56      0.65        18
           1       0.60      0.67      0.63         9
           2       0.67      0.36      0.47        22
           3       0.30      0.43      0.35         7

   micro avg       0.60      0.48      0.53        56
   macro avg       0.58      0.50      0.53        56
weighted avg       0.64      0.48      0.54        56
 samples avg       0.23      0.26      0.23        56


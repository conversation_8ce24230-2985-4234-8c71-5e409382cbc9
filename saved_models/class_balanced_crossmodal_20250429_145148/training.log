2025-04-29 14:51:50,123 - __main__ - INFO - 训练参数:
2025-04-29 14:51:50,123 - __main__ - INFO - audio_dim: 128
2025-04-29 14:51:50,123 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 14:51:50,123 - __main__ - INFO - batch_size: 4
2025-04-29 14:51:50,123 - __main__ - INFO - device: cpu
2025-04-29 14:51:50,123 - __main__ - INFO - dropout: 0.3
2025-04-29 14:51:50,123 - __main__ - INFO - dropout_prob: 0.2
2025-04-29 14:51:50,123 - __main__ - INFO - early_stop: 10
2025-04-29 14:51:50,123 - __main__ - INFO - epochs: 10
2025-04-29 14:51:50,123 - __main__ - INFO - experiment_name: class_balanced_crossmodal_20250429_145148
2025-04-29 14:51:50,123 - __main__ - INFO - factor: 0.5
2025-04-29 14:51:50,123 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 14:51:50,123 - __main__ - INFO - hidden_dim: 256
2025-04-29 14:51:50,123 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 14:51:50,123 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 14:51:50,123 - __main__ - INFO - lr: 0.0003
2025-04-29 14:51:50,123 - __main__ - INFO - mask_prob: 0.15
2025-04-29 14:51:50,123 - __main__ - INFO - mask_ratio: 0.25
2025-04-29 14:51:50,123 - __main__ - INFO - min_lr: 1e-06
2025-04-29 14:51:50,123 - __main__ - INFO - mixup_alpha: 0.3
2025-04-29 14:51:50,123 - __main__ - INFO - mixup_prob: 0.3
2025-04-29 14:51:50,123 - __main__ - INFO - noise_level: 0.15
2025-04-29 14:51:50,123 - __main__ - INFO - noise_prob: 0.2
2025-04-29 14:51:50,123 - __main__ - INFO - num_workers: 4
2025-04-29 14:51:50,123 - __main__ - INFO - patience: 5
2025-04-29 14:51:50,123 - __main__ - INFO - save_all_epochs: False
2025-04-29 14:51:50,123 - __main__ - INFO - save_dir: ./saved_models/class_balanced_crossmodal_20250429_145148
2025-04-29 14:51:50,123 - __main__ - INFO - scheduler: reduce
2025-04-29 14:51:50,123 - __main__ - INFO - seed: 42
2025-04-29 14:51:50,124 - __main__ - INFO - symptom_files: []
2025-04-29 14:51:50,124 - __main__ - INFO - text_dim: 768
2025-04-29 14:51:50,124 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 14:51:50,124 - __main__ - INFO - use_augmentation: True
2025-04-29 14:51:50,124 - __main__ - INFO - visual_dim: 1024
2025-04-29 14:51:50,124 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 14:51:50,124 - __main__ - INFO - visualize: False
2025-04-29 14:51:50,124 - __main__ - INFO - weight_decay: 0.0001
2025-04-29 14:51:50,124 - __main__ - INFO - 准备数据...
2025-04-29 14:51:50,124 - root - INFO - 数据增强参数：
2025-04-29 14:51:50,124 - root - INFO -   noise_prob: 0.2
2025-04-29 14:51:50,124 - root - INFO -   noise_level: 0.15
2025-04-29 14:51:50,124 - root - INFO -   mask_prob: 0.15
2025-04-29 14:51:50,124 - root - INFO -   mask_ratio: 0.25
2025-04-29 14:51:50,124 - root - INFO -   mixup_prob: 0.3
2025-04-29 14:51:50,124 - root - INFO -   mixup_alpha: 0.3
2025-04-29 14:51:50,124 - root - INFO -   modal_dropout_prob: 0.2
2025-04-29 14:51:50,125 - root - INFO - 情绪症状: 阳性样本 105 (27.78%)
2025-04-29 14:51:50,125 - root - INFO - 躯体症状: 阳性样本 83 (21.96%)
2025-04-29 14:51:50,125 - root - INFO - 认知症状: 阳性样本 108 (28.57%)
2025-04-29 14:51:50,125 - root - INFO - 行为症状: 阳性样本 59 (15.61%)
2025-04-29 14:51:50,125 - root - INFO - 加载特征...
2025-04-29 14:51:50,169 - root - INFO - 特征形状：
2025-04-29 14:51:50,169 - root - INFO -   视觉特征: (378, 1024)
2025-04-29 14:51:50,169 - root - INFO -   音频特征: (378, 128)
2025-04-29 14:51:50,169 - root - INFO -   文本特征: (378, 768)
2025-04-29 14:51:50,169 - root - INFO -   标签: (378, 4)
2025-04-29 14:51:50,169 - root - INFO - 创建数据加载器...
2025-04-29 14:51:50,169 - root - INFO - 使用数据增强进行训练
2025-04-29 14:51:50,173 - root - INFO - 训练集样本数: 264
2025-04-29 14:51:50,173 - root - INFO - 验证集样本数: 56
2025-04-29 14:51:50,173 - root - INFO - 测试集样本数: 58
2025-04-29 14:51:50,174 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 14:52:00,303 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 14:52:00,304 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 14:52:00,304 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 14:52:00,304 - __main__ - INFO - 标签形状: torch.Size([4, 4])
2025-04-29 14:52:00,304 - __main__ - INFO - 类别数量: 4
2025-04-29 14:52:00,304 - __main__ - INFO - 使用设备: cpu
2025-04-29 14:52:00,333 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 14:52:00,334 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 14:52:00,334 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 14:52:00,334 - __main__ - INFO - 开始训练...
2025-04-29 14:52:00,853 - root - INFO - 使用BCEWithLogitsLoss作为损失函数，应用类别权重
2025-04-29 14:52:51,795 - root - INFO - Epoch 1/10 - Train Loss: 1.3198, Train Acc: 0.5275, Val Loss: 0.2366, Val Acc: 0.5385
2025-04-29 14:52:51,871 - root - INFO - 发现更好的模型，验证损失: 0.2366
2025-04-29 14:52:57,354 - root - WARNING - 跳过包含NaN值的批次 36
2025-04-29 14:53:42,844 - root - INFO - Epoch 2/10 - Train Loss: 1.2503, Train Acc: 0.5490, Val Loss: 0.2346, Val Acc: 0.5865
2025-04-29 14:53:42,863 - root - INFO - 发现更好的模型，验证损失: 0.2346
2025-04-29 14:54:34,021 - root - INFO - Epoch 3/10 - Train Loss: 1.2068, Train Acc: 0.5322, Val Loss: 0.2383, Val Acc: 0.5625
2025-04-29 14:54:34,022 - root - INFO - 验证损失未改善。早停计数: 1/5
2025-04-29 14:54:39,195 - root - WARNING - 跳过包含NaN值的批次 5
2025-04-29 14:55:25,012 - root - INFO - Epoch 4/10 - Train Loss: 1.1774, Train Acc: 0.5779, Val Loss: 0.2085, Val Acc: 0.6538
2025-04-29 14:55:25,030 - root - INFO - 发现更好的模型，验证损失: 0.2085
2025-04-29 14:56:16,108 - root - INFO - Epoch 5/10 - Train Loss: 1.1213, Train Acc: 0.5852, Val Loss: 0.1990, Val Acc: 0.6875
2025-04-29 14:56:16,126 - root - INFO - 发现更好的模型，验证损失: 0.1990
2025-04-29 14:57:07,162 - root - INFO - Epoch 6/10 - Train Loss: 1.1984, Train Acc: 0.5511, Val Loss: 0.1996, Val Acc: 0.6731
2025-04-29 14:57:07,163 - root - INFO - 验证损失未改善。早停计数: 1/5
2025-04-29 14:57:12,875 - root - WARNING - 跳过包含NaN值的批次 38
2025-04-29 14:57:58,309 - root - INFO - Epoch 7/10 - Train Loss: 1.0997, Train Acc: 0.5750, Val Loss: 0.2072, Val Acc: 0.6490
2025-04-29 14:57:58,310 - root - INFO - 验证损失未改善。早停计数: 2/5
2025-04-29 14:58:49,466 - root - INFO - Epoch 8/10 - Train Loss: 1.1060, Train Acc: 0.5843, Val Loss: 0.1929, Val Acc: 0.6875
2025-04-29 14:58:49,486 - root - INFO - 发现更好的模型，验证损失: 0.1929
2025-04-29 14:59:41,009 - root - INFO - Epoch 9/10 - Train Loss: 1.0999, Train Acc: 0.5862, Val Loss: 0.1993, Val Acc: 0.6875
2025-04-29 14:59:41,010 - root - INFO - 验证损失未改善。早停计数: 1/5
2025-04-29 15:00:32,192 - root - INFO - Epoch 10/10 - Train Loss: 1.0510, Train Acc: 0.5938, Val Loss: 0.1903, Val Acc: 0.7548
2025-04-29 15:00:32,213 - root - INFO - 发现更好的模型，验证损失: 0.1903
2025-04-29 15:00:32,216 - __main__ - INFO - 使用BCEWithLogitsLoss作为损失函数，应用类别权重
2025-04-29 15:00:32,216 - __main__ - INFO - 在测试集上评估最佳模型...
2025-04-29 15:00:57,388 - __main__ - INFO - 测试集损失: 0.9207, 测试集准确率: 0.7371, 测试集F1: 0.5422
2025-04-29 15:00:57,388 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.67      0.60      0.63        20
           1       0.50      0.73      0.59        15
           2       0.41      0.44      0.42        16
           3       0.39      0.78      0.52         9

   micro avg       0.49      0.62      0.55        60
   macro avg       0.49      0.64      0.54        60
weighted avg       0.52      0.62      0.55        60
 samples avg       0.22      0.36      0.26        60

2025-04-29 15:00:57,396 - __main__ - INFO - 测试损失: 0.9207, 测试准确率: 0.7371, 测试F1分数: 0.5422
2025-04-29 15:00:57,396 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.67      0.60      0.63        20
           1       0.50      0.73      0.59        15
           2       0.41      0.44      0.42        16
           3       0.39      0.78      0.52         9

   micro avg       0.49      0.62      0.55        60
   macro avg       0.49      0.64      0.54        60
weighted avg       0.52      0.62      0.55        60
 samples avg       0.22      0.36      0.26        60

2025-04-29 15:00:57,398 - __main__ - INFO - 实验完成! 所有结果已保存到 ./saved_models/class_balanced_crossmodal_20250429_145148

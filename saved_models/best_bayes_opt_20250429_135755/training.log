2025-04-29 13:57:57,723 - __main__ - INFO - 训练参数:
2025-04-29 13:57:57,724 - __main__ - INFO - audio_dim: 128
2025-04-29 13:57:57,724 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 13:57:57,724 - __main__ - INFO - batch_size: 4
2025-04-29 13:57:57,724 - __main__ - INFO - device: cpu
2025-04-29 13:57:57,724 - __main__ - INFO - dropout: 0.3
2025-04-29 13:57:57,724 - __main__ - INFO - dropout_prob: 0.1792
2025-04-29 13:57:57,724 - __main__ - INFO - early_stop: 10
2025-04-29 13:57:57,724 - __main__ - INFO - epochs: 10
2025-04-29 13:57:57,724 - __main__ - INFO - experiment_name: best_bayes_opt_20250429_135755
2025-04-29 13:57:57,724 - __main__ - INFO - factor: 0.5
2025-04-29 13:57:57,724 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 13:57:57,724 - __main__ - INFO - hidden_dim: 256
2025-04-29 13:57:57,724 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 13:57:57,724 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 13:57:57,724 - __main__ - INFO - lr: 0.0003
2025-04-29 13:57:57,724 - __main__ - INFO - mask_prob: 0.4767
2025-04-29 13:57:57,724 - __main__ - INFO - mask_ratio: 0.3319
2025-04-29 13:57:57,724 - __main__ - INFO - min_lr: 1e-06
2025-04-29 13:57:57,724 - __main__ - INFO - mixup_alpha: 0.1877
2025-04-29 13:57:57,724 - __main__ - INFO - mixup_prob: 0.333
2025-04-29 13:57:57,724 - __main__ - INFO - noise_level: 0.0146
2025-04-29 13:57:57,724 - __main__ - INFO - noise_prob: 0.0776
2025-04-29 13:57:57,724 - __main__ - INFO - num_workers: 2
2025-04-29 13:57:57,724 - __main__ - INFO - patience: 5
2025-04-29 13:57:57,724 - __main__ - INFO - save_all_epochs: False
2025-04-29 13:57:57,724 - __main__ - INFO - save_dir: ./saved_models/best_bayes_opt_20250429_135755
2025-04-29 13:57:57,724 - __main__ - INFO - scheduler: reduce
2025-04-29 13:57:57,724 - __main__ - INFO - seed: 42
2025-04-29 13:57:57,724 - __main__ - INFO - symptom_files: []
2025-04-29 13:57:57,724 - __main__ - INFO - text_dim: 768
2025-04-29 13:57:57,724 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 13:57:57,724 - __main__ - INFO - use_augmentation: True
2025-04-29 13:57:57,724 - __main__ - INFO - visual_dim: 1024
2025-04-29 13:57:57,724 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 13:57:57,724 - __main__ - INFO - visualize: True
2025-04-29 13:57:57,724 - __main__ - INFO - weight_decay: 1e-05
2025-04-29 13:57:57,724 - __main__ - INFO - 准备数据...
2025-04-29 13:57:57,780 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 13:58:04,360 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 13:58:04,360 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 13:58:04,360 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 13:58:04,360 - __main__ - INFO - 类别数量: 4
2025-04-29 13:58:04,360 - __main__ - INFO - 使用设备: cpu
2025-04-29 13:58:04,388 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 13:58:04,389 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 13:58:04,389 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 13:58:04,389 - __main__ - INFO - 使用BCEWithLogitsLoss损失函数
2025-04-29 13:58:04,897 - __main__ - INFO - 开始训练...
2025-04-29 13:58:07,081 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:58:07,185 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:58:28,879 - __main__ - INFO - Epoch 1/10 - Train Loss: 0.8326, Train Acc: 0.6045, Train F1: 0.2771, Val Loss: 0.7757, Val Acc: 0.6161, Val F1: 0.3485, LR: 0.00030000
2025-04-29 13:58:28,971 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.7757)
2025-04-29 13:58:30,925 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:58:31,185 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:58:52,879 - __main__ - INFO - Epoch 2/10 - Train Loss: 0.7888, Train Acc: 0.6250, Train F1: 0.2883, Val Loss: 0.6742, Val Acc: 0.6830, Val F1: 0.4071, LR: 0.00030000
2025-04-29 13:58:52,911 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.6742)
2025-04-29 13:58:54,639 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:58:55,003 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:59:16,900 - __main__ - INFO - Epoch 3/10 - Train Loss: 0.6899, Train Acc: 0.6758, Train F1: 0.3461, Val Loss: 0.6628, Val Acc: 0.6964, Val F1: 0.4114, LR: 0.00030000
2025-04-29 13:59:16,929 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.6628)
2025-04-29 13:59:18,606 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:59:19,173 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:59:40,876 - __main__ - INFO - Epoch 4/10 - Train Loss: 0.6733, Train Acc: 0.6982, Train F1: 0.3374, Val Loss: 0.6636, Val Acc: 0.7188, Val F1: 0.4712, LR: 0.00030000
2025-04-29 13:59:40,878 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 13:59:42,649 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 13:59:42,872 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:00:04,863 - __main__ - INFO - Epoch 5/10 - Train Loss: 0.6702, Train Acc: 0.7100, Train F1: 0.3426, Val Loss: 0.6474, Val Acc: 0.6964, Val F1: 0.4939, LR: 0.00030000
2025-04-29 14:00:04,900 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.6474)
2025-04-29 14:00:07,005 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:00:07,191 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:00:28,925 - __main__ - INFO - Epoch 6/10 - Train Loss: 0.6150, Train Acc: 0.7344, Train F1: 0.3604, Val Loss: 0.6457, Val Acc: 0.6964, Val F1: 0.4686, LR: 0.00030000
2025-04-29 14:00:28,961 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.6457)
2025-04-29 14:00:31,287 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:00:52,926 - __main__ - INFO - Epoch 7/10 - Train Loss: 0.6075, Train Acc: 0.7356, Train F1: 0.3421, Val Loss: 0.6268, Val Acc: 0.7009, Val F1: 0.4312, LR: 0.00030000
2025-04-29 14:00:52,961 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.6268)
2025-04-29 14:00:54,748 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:00:54,998 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:01:16,935 - __main__ - INFO - Epoch 8/10 - Train Loss: 0.5572, Train Acc: 0.7568, Train F1: 0.4060, Val Loss: 0.5719, Val Acc: 0.7545, Val F1: 0.5067, LR: 0.00030000
2025-04-29 14:01:16,966 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.5719)
2025-04-29 14:01:18,814 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:01:18,890 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:01:40,921 - __main__ - INFO - Epoch 9/10 - Train Loss: 0.5618, Train Acc: 0.7471, Train F1: 0.3267, Val Loss: 0.5446, Val Acc: 0.7366, Val F1: 0.3746, LR: 0.00030000
2025-04-29 14:01:40,954 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.5446)
2025-04-29 14:01:42,944 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:01:43,046 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 14:02:04,919 - __main__ - INFO - Epoch 10/10 - Train Loss: 0.5369, Train Acc: 0.7578, Train F1: 0.3327, Val Loss: 0.4928, Val Acc: 0.7679, Val F1: 0.3041, LR: 0.00030000
2025-04-29 14:02:04,954 - __main__ - INFO - 模型已保存到 ./saved_models/best_bayes_opt_20250429_135755/best_model.pth (验证损失: 0.4928)
2025-04-29 14:02:05,228 - __main__ - INFO - 训练完成! 最佳验证损失: 0.4928 (Epoch 10)
2025-04-29 14:02:05,228 - __main__ - INFO - 在测试集上评估最佳模型...
2025-04-29 14:02:17,117 - __main__ - INFO - 测试集损失: 0.4638, 测试集准确率: 0.8060, 测试集F1: 0.3730
2025-04-29 14:02:17,118 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.86      0.33      0.48        18
           1       0.50      0.11      0.18         9
           2       0.88      0.32      0.47        22
           3       0.50      0.29      0.36         7

   micro avg       0.76      0.29      0.42        56
   macro avg       0.68      0.26      0.37        56
weighted avg       0.76      0.29      0.41        56
 samples avg       0.14      0.14      0.14        56

2025-04-29 14:02:17,126 - __main__ - INFO - 测试损失: 0.4638, 测试准确率: 0.8060, 测试F1分数: 0.3730
2025-04-29 14:02:17,126 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.86      0.33      0.48        18
           1       0.50      0.11      0.18         9
           2       0.88      0.32      0.47        22
           3       0.50      0.29      0.36         7

   micro avg       0.76      0.29      0.42        56
   macro avg       0.68      0.26      0.37        56
weighted avg       0.76      0.29      0.41        56
 samples avg       0.14      0.14      0.14        56

2025-04-29 14:02:17,128 - __main__ - INFO - 实验完成! 所有结果已保存到 ./saved_models/best_bayes_opt_20250429_135755

2025-04-29 11:57:09,910 - __main__ - INFO - 训练参数:
2025-04-29 11:57:09,910 - __main__ - INFO - audio_dim: 128
2025-04-29 11:57:09,910 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 11:57:09,910 - __main__ - INFO - batch_size: 4
2025-04-29 11:57:09,910 - __main__ - INFO - device: cpu
2025-04-29 11:57:09,910 - __main__ - INFO - dropout: 0.3
2025-04-29 11:57:09,910 - __main__ - INFO - dropout_prob: 0.2
2025-04-29 11:57:09,910 - __main__ - INFO - early_stop: 10
2025-04-29 11:57:09,910 - __main__ - INFO - epochs: 10
2025-04-29 11:57:09,910 - __main__ - INFO - experiment_name: optimized_crossmodal_20250429_115708
2025-04-29 11:57:09,910 - __main__ - INFO - factor: 0.5
2025-04-29 11:57:09,910 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 11:57:09,910 - __main__ - INFO - hidden_dim: 256
2025-04-29 11:57:09,910 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 11:57:09,910 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 11:57:09,910 - __main__ - INFO - lr: 0.0003
2025-04-29 11:57:09,910 - __main__ - INFO - mask_prob: 0.15
2025-04-29 11:57:09,910 - __main__ - INFO - mask_ratio: 0.25
2025-04-29 11:57:09,910 - __main__ - INFO - min_lr: 1e-06
2025-04-29 11:57:09,910 - __main__ - INFO - mixup_alpha: 0.3
2025-04-29 11:57:09,910 - __main__ - INFO - mixup_prob: 0.3
2025-04-29 11:57:09,910 - __main__ - INFO - noise_level: 0.15
2025-04-29 11:57:09,910 - __main__ - INFO - noise_prob: 0.2
2025-04-29 11:57:09,910 - __main__ - INFO - num_workers: 2
2025-04-29 11:57:09,910 - __main__ - INFO - patience: 5
2025-04-29 11:57:09,910 - __main__ - INFO - save_all_epochs: False
2025-04-29 11:57:09,910 - __main__ - INFO - save_dir: ./saved_models/optimized_crossmodal_20250429_115708
2025-04-29 11:57:09,910 - __main__ - INFO - scheduler: reduce
2025-04-29 11:57:09,910 - __main__ - INFO - seed: 42
2025-04-29 11:57:09,910 - __main__ - INFO - symptom_files: []
2025-04-29 11:57:09,910 - __main__ - INFO - text_dim: 768
2025-04-29 11:57:09,910 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 11:57:09,910 - __main__ - INFO - use_augmentation: True
2025-04-29 11:57:09,910 - __main__ - INFO - visual_dim: 1024
2025-04-29 11:57:09,910 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 11:57:09,910 - __main__ - INFO - visualize: True
2025-04-29 11:57:09,910 - __main__ - INFO - weight_decay: 1e-05
2025-04-29 11:57:09,910 - __main__ - INFO - 准备数据...
2025-04-29 11:57:09,966 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 11:57:21,527 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 11:57:21,528 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 11:57:21,528 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 11:57:21,528 - __main__ - INFO - 类别数量: 4
2025-04-29 11:57:21,528 - __main__ - INFO - 使用设备: cpu
2025-04-29 11:57:21,556 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 11:57:21,556 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 11:57:21,556 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 11:57:21,556 - __main__ - INFO - 使用BCEWithLogitsLoss损失函数
2025-04-29 11:57:22,071 - __main__ - INFO - 开始训练...
2025-04-29 11:57:24,329 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:57:24,424 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:57:46,121 - __main__ - INFO - Epoch 1/10 - Train Loss: 0.8183, Train Acc: 0.6074, Train F1: 0.2862, Val Loss: 0.7015, Val Acc: 0.6607, Val F1: 0.4165, LR: 0.00030000
2025-04-29 11:57:46,204 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.7015)
2025-04-29 11:57:48,215 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:57:48,471 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:58:10,205 - __main__ - INFO - Epoch 2/10 - Train Loss: 0.7712, Train Acc: 0.6309, Train F1: 0.2830, Val Loss: 0.7062, Val Acc: 0.6562, Val F1: 0.3854, LR: 0.00030000
2025-04-29 11:58:10,206 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 11:58:11,882 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:58:12,218 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:58:34,114 - __main__ - INFO - Epoch 3/10 - Train Loss: 0.6899, Train Acc: 0.6875, Train F1: 0.3600, Val Loss: 0.6687, Val Acc: 0.6875, Val F1: 0.4661, LR: 0.00030000
2025-04-29 11:58:34,143 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.6687)
2025-04-29 11:58:35,842 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:58:36,372 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:58:58,088 - __main__ - INFO - Epoch 4/10 - Train Loss: 0.6582, Train Acc: 0.6924, Train F1: 0.3349, Val Loss: 0.6229, Val Acc: 0.6920, Val F1: 0.4387, LR: 0.00030000
2025-04-29 11:58:58,118 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.6229)
2025-04-29 11:58:59,905 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:59:00,126 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:59:22,112 - __main__ - INFO - Epoch 5/10 - Train Loss: 0.6871, Train Acc: 0.6836, Train F1: 0.2893, Val Loss: 0.6116, Val Acc: 0.6830, Val F1: 0.3916, LR: 0.00030000
2025-04-29 11:59:22,133 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.6116)
2025-04-29 11:59:24,360 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:59:24,557 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:59:46,301 - __main__ - INFO - Epoch 6/10 - Train Loss: 0.6199, Train Acc: 0.7285, Train F1: 0.3842, Val Loss: 0.5753, Val Acc: 0.7411, Val F1: 0.5084, LR: 0.00030000
2025-04-29 11:59:46,336 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.5753)
2025-04-29 11:59:48,487 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 11:59:48,906 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:00:10,604 - __main__ - INFO - Epoch 7/10 - Train Loss: 0.5894, Train Acc: 0.7246, Train F1: 0.2781, Val Loss: 0.5614, Val Acc: 0.7009, Val F1: 0.3488, LR: 0.00030000
2025-04-29 12:00:10,640 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.5614)
2025-04-29 12:00:13,076 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:00:34,886 - __main__ - INFO - Epoch 8/10 - Train Loss: 0.5650, Train Acc: 0.7356, Train F1: 0.2881, Val Loss: 0.5418, Val Acc: 0.7188, Val F1: 0.3931, LR: 0.00030000
2025-04-29 12:00:34,916 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.5418)
2025-04-29 12:00:36,886 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:00:58,986 - __main__ - INFO - Epoch 9/10 - Train Loss: 0.5729, Train Acc: 0.7519, Train F1: 0.2712, Val Loss: 0.5359, Val Acc: 0.7232, Val F1: 0.4027, LR: 0.00030000
2025-04-29 12:00:59,022 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_115708/best_model.pth (验证损失: 0.5359)
2025-04-29 12:01:00,690 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:01:01,059 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:01:01,365 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:01:23,003 - __main__ - INFO - Epoch 10/10 - Train Loss: 0.5633, Train Acc: 0.7500, Train F1: 0.3065, Val Loss: 0.5891, Val Acc: 0.7277, Val F1: 0.2987, LR: 0.00030000
2025-04-29 12:01:23,004 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 12:01:23,285 - __main__ - INFO - 训练完成! 最佳验证损失: 0.5359 (Epoch 9)
2025-04-29 12:01:23,285 - __main__ - INFO - 在测试集上评估最佳模型...
2025-04-29 12:01:34,998 - __main__ - INFO - 测试集损失: 0.5308, 测试集准确率: 0.7629, 测试集F1: 0.3737
2025-04-29 12:01:34,999 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.75      0.33      0.46        18
           1       0.14      0.11      0.12         9
           2       0.70      0.32      0.44        22
           3       0.40      0.57      0.47         7

   micro avg       0.51      0.32      0.40        56
   macro avg       0.50      0.33      0.37        56
weighted avg       0.59      0.32      0.40        56
 samples avg       0.13      0.16      0.13        56

2025-04-29 12:01:35,006 - __main__ - INFO - 测试损失: 0.5308, 测试准确率: 0.7629, 测试F1分数: 0.3737
2025-04-29 12:01:35,006 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.75      0.33      0.46        18
           1       0.14      0.11      0.12         9
           2       0.70      0.32      0.44        22
           3       0.40      0.57      0.47         7

   micro avg       0.51      0.32      0.40        56
   macro avg       0.50      0.33      0.37        56
weighted avg       0.59      0.32      0.40        56
 samples avg       0.13      0.16      0.13        56

2025-04-29 12:01:35,008 - __main__ - INFO - 实验完成! 所有结果已保存到 ./saved_models/optimized_crossmodal_20250429_115708

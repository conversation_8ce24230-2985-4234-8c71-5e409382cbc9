2025-04-29 12:11:09,757 - __main__ - INFO - 训练参数:
2025-04-29 12:11:09,757 - __main__ - INFO - audio_dim: 128
2025-04-29 12:11:09,757 - __main__ - INFO - audio_path: ./processed_features/audio
2025-04-29 12:11:09,757 - __main__ - INFO - batch_size: 4
2025-04-29 12:11:09,757 - __main__ - INFO - device: cpu
2025-04-29 12:11:09,757 - __main__ - INFO - dropout: 0.3
2025-04-29 12:11:09,757 - __main__ - INFO - dropout_prob: 0.2
2025-04-29 12:11:09,757 - __main__ - INFO - early_stop: 10
2025-04-29 12:11:09,757 - __main__ - INFO - epochs: 10
2025-04-29 12:11:09,757 - __main__ - INFO - experiment_name: optimized_crossmodal_20250429_121108
2025-04-29 12:11:09,757 - __main__ - INFO - factor: 0.5
2025-04-29 12:11:09,757 - __main__ - INFO - gradient_clip: 1.0
2025-04-29 12:11:09,757 - __main__ - INFO - hidden_dim: 256
2025-04-29 12:11:09,757 - __main__ - INFO - l2_lambda: 1e-05
2025-04-29 12:11:09,757 - __main__ - INFO - label_file: ./processed_features/train_labels.csv
2025-04-29 12:11:09,757 - __main__ - INFO - lr: 0.0003
2025-04-29 12:11:09,757 - __main__ - INFO - mask_prob: 0.15
2025-04-29 12:11:09,757 - __main__ - INFO - mask_ratio: 0.25
2025-04-29 12:11:09,757 - __main__ - INFO - min_lr: 1e-06
2025-04-29 12:11:09,757 - __main__ - INFO - mixup_alpha: 0.3
2025-04-29 12:11:09,757 - __main__ - INFO - mixup_prob: 0.3
2025-04-29 12:11:09,757 - __main__ - INFO - noise_level: 0.15
2025-04-29 12:11:09,757 - __main__ - INFO - noise_prob: 0.2
2025-04-29 12:11:09,757 - __main__ - INFO - num_workers: 2
2025-04-29 12:11:09,757 - __main__ - INFO - patience: 5
2025-04-29 12:11:09,757 - __main__ - INFO - save_all_epochs: False
2025-04-29 12:11:09,757 - __main__ - INFO - save_dir: ./saved_models/optimized_crossmodal_20250429_121108
2025-04-29 12:11:09,757 - __main__ - INFO - scheduler: reduce
2025-04-29 12:11:09,757 - __main__ - INFO - seed: 42
2025-04-29 12:11:09,757 - __main__ - INFO - symptom_files: []
2025-04-29 12:11:09,757 - __main__ - INFO - text_dim: 768
2025-04-29 12:11:09,757 - __main__ - INFO - text_path: ./processed_features/text
2025-04-29 12:11:09,757 - __main__ - INFO - use_augmentation: True
2025-04-29 12:11:09,757 - __main__ - INFO - visual_dim: 1024
2025-04-29 12:11:09,757 - __main__ - INFO - visual_path: ./processed_features/visual
2025-04-29 12:11:09,757 - __main__ - INFO - visualize: True
2025-04-29 12:11:09,757 - __main__ - INFO - weight_decay: 1e-05
2025-04-29 12:11:09,757 - __main__ - INFO - 准备数据...
2025-04-29 12:11:09,809 - __main__ - INFO - 数据加载完成: 训练集 264个样本, 验证集 56个样本, 测试集 58个样本
2025-04-29 12:11:16,346 - __main__ - INFO - 视觉特征形状: torch.Size([4, 1024])
2025-04-29 12:11:16,346 - __main__ - INFO - 音频特征形状: torch.Size([4, 128])
2025-04-29 12:11:16,346 - __main__ - INFO - 文本特征形状: torch.Size([4, 768])
2025-04-29 12:11:16,346 - __main__ - INFO - 类别数量: 4
2025-04-29 12:11:16,347 - __main__ - INFO - 使用设备: cpu
2025-04-29 12:11:16,372 - __main__ - INFO - 模型架构:
CrossModalAttentionFusion(
  (visual_encoder): ModalityEncoder(
    (fc1): Linear(in_features=1024, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_encoder): ModalityEncoder(
    (fc1): Linear(in_features=128, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_encoder): ModalityEncoder(
    (fc1): Linear(in_features=768, out_features=512, bias=True)
    (fc2): Linear(in_features=512, out_features=256, bias=True)
    (dropout): Dropout(p=0.3, inplace=False)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (audio_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (text_recalib): FeatureRecalibrationModule(
    (avg_pool): AdaptiveAvgPool1d(output_size=1)
    (fc): Sequential(
      (0): Linear(in_features=256, out_features=16, bias=False)
      (1): ReLU(inplace=True)
      (2): Linear(in_features=16, out_features=256, bias=False)
      (3): Sigmoid()
    )
    (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (visual_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_to_text_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_visual_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_to_audio_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (visual_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (audio_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (text_self_attn): AttentionModule(
    (query_proj): Linear(in_features=256, out_features=256, bias=True)
    (key_proj): Linear(in_features=256, out_features=256, bias=True)
    (value_proj): Linear(in_features=256, out_features=256, bias=True)
    (layer_norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (adaptive_weight): AdaptiveWeightModule(
    (fc): Sequential(
      (0): Linear(in_features=768, out_features=256, bias=True)
      (1): ReLU()
      (2): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (3): Linear(in_features=256, out_features=3, bias=True)
      (4): Softmax(dim=1)
    )
  )
  (fusion_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.3, inplace=False)
    (3): Linear(in_features=512, out_features=256, bias=True)
    (4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    (5): ReLU()
    (6): Dropout(p=0.3, inplace=False)
  )
  (classifier): Linear(in_features=256, out_features=4, bias=True)
  (dropout): Dropout(p=0.3, inplace=False)
  (batch_norm): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
)
2025-04-29 12:11:16,373 - __main__ - INFO - 模型总参数: 3,912,455
2025-04-29 12:11:16,373 - __main__ - INFO - 可训练参数: 3,912,455
2025-04-29 12:11:16,373 - __main__ - INFO - 使用BCEWithLogitsLoss损失函数
2025-04-29 12:11:16,884 - __main__ - INFO - 开始训练...
2025-04-29 12:11:18,988 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:11:40,789 - __main__ - INFO - Epoch 1/10 - Train Loss: 0.8058, Train Acc: 0.6154, Train F1: 0.2967, Val Loss: 0.6833, Val Acc: 0.6696, Val F1: 0.4021, LR: 0.00030000
2025-04-29 12:11:40,867 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.6833)
2025-04-29 12:11:42,602 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:11:42,916 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:04,789 - __main__ - INFO - Epoch 2/10 - Train Loss: 0.7239, Train Acc: 0.6602, Train F1: 0.3248, Val Loss: 0.6478, Val Acc: 0.7098, Val F1: 0.4422, LR: 0.00030000
2025-04-29 12:12:04,817 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.6478)
2025-04-29 12:12:06,435 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:06,850 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:28,773 - __main__ - INFO - Epoch 3/10 - Train Loss: 0.7066, Train Acc: 0.6689, Train F1: 0.3173, Val Loss: 0.6592, Val Acc: 0.7009, Val F1: 0.5254, LR: 0.00030000
2025-04-29 12:12:28,773 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 12:12:31,031 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:31,102 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:52,771 - __main__ - INFO - Epoch 4/10 - Train Loss: 0.6400, Train Acc: 0.7090, Train F1: 0.3648, Val Loss: 0.5840, Val Acc: 0.7321, Val F1: 0.4823, LR: 0.00030000
2025-04-29 12:12:52,800 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.5840)
2025-04-29 12:12:54,654 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:12:54,858 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:13:16,850 - __main__ - INFO - Epoch 5/10 - Train Loss: 0.6633, Train Acc: 0.6895, Train F1: 0.3152, Val Loss: 0.5752, Val Acc: 0.7277, Val F1: 0.4127, LR: 0.00030000
2025-04-29 12:13:16,882 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.5752)
2025-04-29 12:13:18,675 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:13:18,723 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:13:40,835 - __main__ - INFO - Epoch 6/10 - Train Loss: 0.5838, Train Acc: 0.7402, Train F1: 0.3515, Val Loss: 0.5877, Val Acc: 0.7321, Val F1: 0.4634, LR: 0.00030000
2025-04-29 12:13:40,835 - __main__ - INFO - 验证损失未改善，早停计数: 1/10
2025-04-29 12:13:42,946 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:13:43,068 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:04,785 - __main__ - INFO - Epoch 7/10 - Train Loss: 0.5884, Train Acc: 0.7090, Train F1: 0.2081, Val Loss: 0.6643, Val Acc: 0.7589, Val F1: 0.4895, LR: 0.00030000
2025-04-29 12:14:04,785 - __main__ - INFO - 验证损失未改善，早停计数: 2/10
2025-04-29 12:14:06,589 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:06,826 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:28,815 - __main__ - INFO - Epoch 8/10 - Train Loss: 0.5606, Train Acc: 0.7529, Train F1: 0.3329, Val Loss: 0.5657, Val Acc: 0.7723, Val F1: 0.4801, LR: 0.00030000
2025-04-29 12:14:28,850 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.5657)
2025-04-29 12:14:30,735 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:30,809 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:52,838 - __main__ - INFO - Epoch 9/10 - Train Loss: 0.5345, Train Acc: 0.7715, Train F1: 0.3609, Val Loss: 0.5653, Val Acc: 0.7545, Val F1: 0.4169, LR: 0.00030000
2025-04-29 12:14:52,868 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.5653)
2025-04-29 12:14:54,887 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:14:54,985 - __main__ - WARNING - 跳过包含NaN值的批次
2025-04-29 12:15:16,890 - __main__ - INFO - Epoch 10/10 - Train Loss: 0.5186, Train Acc: 0.7734, Train F1: 0.3537, Val Loss: 0.5504, Val Acc: 0.7411, Val F1: 0.3785, LR: 0.00030000
2025-04-29 12:15:16,919 - __main__ - INFO - 模型已保存到 ./saved_models/optimized_crossmodal_20250429_121108/best_model.pth (验证损失: 0.5504)
2025-04-29 12:15:17,184 - __main__ - INFO - 训练完成! 最佳验证损失: 0.5504 (Epoch 10)
2025-04-29 12:15:17,184 - __main__ - INFO - 在测试集上评估最佳模型...
2025-04-29 12:15:28,904 - __main__ - INFO - 测试集损失: 0.5172, 测试集准确率: 0.7845, 测试集F1: 0.4127
2025-04-29 12:15:28,904 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.60      0.33      0.43        18
           1       0.38      0.56      0.45         9
           2       0.82      0.41      0.55        22
           3       0.50      0.14      0.22         7

   micro avg       0.58      0.38      0.46        56
   macro avg       0.58      0.36      0.41        56
weighted avg       0.64      0.38      0.45        56
 samples avg       0.22      0.21      0.21        56

2025-04-29 12:15:28,911 - __main__ - INFO - 测试损失: 0.5172, 测试准确率: 0.7845, 测试F1分数: 0.4127
2025-04-29 12:15:28,911 - __main__ - INFO - 分类报告:
              precision    recall  f1-score   support

           0       0.60      0.33      0.43        18
           1       0.38      0.56      0.45         9
           2       0.82      0.41      0.55        22
           3       0.50      0.14      0.22         7

   micro avg       0.58      0.38      0.46        56
   macro avg       0.58      0.36      0.41        56
weighted avg       0.64      0.38      0.45        56
 samples avg       0.22      0.21      0.21        56

2025-04-29 12:15:28,912 - __main__ - INFO - 实验完成! 所有结果已保存到 ./saved_models/optimized_crossmodal_20250429_121108

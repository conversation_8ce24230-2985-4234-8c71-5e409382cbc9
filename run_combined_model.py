#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行结合时序增强和跨模态一致性增强的模型
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
from temporal_enhanced_model import TemporalEnhancedModel
from combined_temporal_consistency_processor import prepare_combined_data, set_seed

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行结合时序增强和跨模态一致性增强的模型')

    # 数据参数
    parser.add_argument('--data_dir', type=str, default='./shuju', help='特征数据目录')
    parser.add_argument('--label_file', type=str, default='./symptom_label_template.xlsx', help='标签文件路径')
    parser.add_argument('--output_dir', type=str, default='./combined_results', help='输出目录')

    # 模型参数
    parser.add_argument('--max_seq_len', type=int, default=100, help='最大序列长度')
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--num_layers', type=int, default=2, help='LSTM层数')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout比例')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.0005, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.0001, help='权重衰减')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')

    # 数据增强参数
    parser.add_argument('--augment_prob', type=float, default=0.2, help='数据增强概率')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='设备')

    return parser.parse_args()

def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, device, epochs, patience, output_dir):
    """
    训练模型

    参数:
    - model: 模型
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - criterion: 损失函数
    - optimizer: 优化器
    - scheduler: 学习率调度器
    - device: 设备
    - epochs: 训练轮数
    - patience: 早停耐心值
    - output_dir: 输出目录

    返回:
    - 训练历史记录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 初始化训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'val_accuracy': []
    }

    # 初始化早停变量
    best_val_loss = float('inf')
    best_model_state = None
    patience_counter = 0

    # 训练循环
    for epoch in range(epochs):
        # 训练模式
        model.train()
        train_loss = 0.0

        for batch_idx, (visual_batch, audio_batch, text_batch, labels_batch) in enumerate(train_loader):
            # 将数据移动到设备
            visual_batch = visual_batch.to(device)
            audio_batch = audio_batch.to(device)
            text_batch = text_batch.to(device)

            # 清零梯度
            optimizer.zero_grad()

            # 前向传播
            outputs = model(visual_batch, audio_batch, text_batch)

            # 计算损失
            loss = 0.0
            for i, symptom_output in enumerate(outputs):
                symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)
                loss += criterion(symptom_output, symptom_labels)

            # 反向传播
            loss.backward()

            # 更新参数
            optimizer.step()

            # 累加损失
            train_loss += loss.item()

            # 打印进度
            if (batch_idx + 1) % 10 == 0:
                print(f'Epoch {epoch+1}/{epochs} | Batch {batch_idx+1}/{len(train_loader)} | Loss: {loss.item():.4f}')

        # 计算平均训练损失
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)

        # 验证模式
        model.eval()
        val_loss = 0.0
        all_preds = [[] for _ in range(len(outputs))]
        all_labels = [[] for _ in range(len(outputs))]

        with torch.no_grad():
            for visual_batch, audio_batch, text_batch, labels_batch in val_loader:
                # 将数据移动到设备
                visual_batch = visual_batch.to(device)
                audio_batch = audio_batch.to(device)
                text_batch = text_batch.to(device)

                # 前向传播
                outputs = model(visual_batch, audio_batch, text_batch)

                # 计算损失
                batch_loss = 0.0
                for i, symptom_output in enumerate(outputs):
                    symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)
                    batch_loss += criterion(symptom_output, symptom_labels)

                    # 收集预测和标签
                    preds = torch.argmax(symptom_output, dim=1).cpu().numpy()
                    labels = symptom_labels.cpu().numpy()
                    all_preds[i].extend(preds)
                    all_labels[i].extend(labels)

                # 累加损失
                val_loss += batch_loss.item()

        # 计算平均验证损失
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)

        # 计算平均验证准确率
        val_accuracy = np.mean([accuracy_score(all_labels[i], all_preds[i]) for i in range(len(all_preds))])
        history['val_accuracy'].append(val_accuracy)

        # 更新学习率
        scheduler.step(val_loss)

        # 打印验证结果
        print(f'Epoch {epoch+1}/{epochs} | Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f} | Val Accuracy: {val_accuracy:.4f}')

        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict()
            patience_counter = 0

            # 保存最佳模型
            torch.save(model.state_dict(), os.path.join(output_dir, 'combined_model.pth'))
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f'Early stopping at epoch {epoch+1}')
                break

    # 恢复最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    # 保存训练历史记录
    with open(os.path.join(output_dir, 'combined_training_history.json'), 'w') as f:
        json.dump(history, f)

    return history

def evaluate_model(model, test_loader, symptom_types, device, output_dir):
    """
    评估模型

    参数:
    - model: 模型
    - test_loader: 测试数据加载器
    - symptom_types: 症状类型列表
    - device: 设备
    - output_dir: 输出目录

    返回:
    - 评估结果
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 评估模式
    model.eval()

    # 收集所有预测和标签
    all_outputs = [[] for _ in range(len(symptom_types))]
    all_labels = [[] for _ in range(len(symptom_types))]

    with torch.no_grad():
        for visual_batch, audio_batch, text_batch, labels_batch in test_loader:
            # 将数据移动到设备
            visual_batch = visual_batch.to(device)
            audio_batch = audio_batch.to(device)
            text_batch = text_batch.to(device)

            # 前向传播
            outputs = model(visual_batch, audio_batch, text_batch)

            # 收集输出和标签
            for i, symptom_output in enumerate(outputs):
                symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)
                all_outputs[i].extend(symptom_output.cpu().numpy())
                all_labels[i].extend(symptom_labels.cpu().numpy())

    # 计算最佳阈值和评估指标
    results = {}
    for i, symptom_type in enumerate(symptom_types):
        # 转换为numpy数组
        outputs = np.array(all_outputs[i])
        labels = np.array(all_labels[i])

        # 计算预测概率
        probs = np.exp(outputs) / np.sum(np.exp(outputs), axis=1, keepdims=True)
        positive_probs = probs[:, 1]  # 正类的概率

        # 寻找最佳阈值
        best_threshold = 0.5
        best_f1 = 0.0

        for threshold in np.arange(0.1, 0.9, 0.01):
            preds = (positive_probs >= threshold).astype(int)
            f1 = f1_score(labels, preds)

            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

        # 使用最佳阈值进行预测
        preds = (positive_probs >= best_threshold).astype(int)

        # 计算评估指标
        accuracy = accuracy_score(labels, preds)
        precision = precision_score(labels, preds)
        recall = recall_score(labels, preds)
        f1 = f1_score(labels, preds)
        auc = roc_auc_score(labels, positive_probs)

        # 计算混淆矩阵
        tn, fp, fn, tp = confusion_matrix(labels, preds).ravel()

        # 保存结果
        results[symptom_type] = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc,
            'threshold': best_threshold,
            'tp': int(tp),
            'fp': int(fp),
            'tn': int(tn),
            'fn': int(fn)
        }

    # 计算平均指标
    avg_accuracy = np.mean([results[symptom_type]['accuracy'] for symptom_type in symptom_types])
    avg_precision = np.mean([results[symptom_type]['precision'] for symptom_type in symptom_types])
    avg_recall = np.mean([results[symptom_type]['recall'] for symptom_type in symptom_types])
    avg_f1 = np.mean([results[symptom_type]['f1'] for symptom_type in symptom_types])
    avg_auc = np.mean([results[symptom_type]['auc'] for symptom_type in symptom_types])

    results['average'] = {
        'accuracy': avg_accuracy,
        'precision': avg_precision,
        'recall': avg_recall,
        'f1': avg_f1,
        'auc': avg_auc
    }

    # 保存评估结果
    with open(os.path.join(output_dir, 'combined_test_results.json'), 'w') as f:
        json.dump(results, f, indent=4)

    # 打印评估结果
    print('\n评估结果:')
    print(f'平均准确率: {avg_accuracy:.4f}')
    print(f'平均精确率: {avg_precision:.4f}')
    print(f'平均召回率: {avg_recall:.4f}')
    print(f'平均F1分数: {avg_f1:.4f}')
    print(f'平均AUC: {avg_auc:.4f}')

    for symptom_type in symptom_types:
        print(f'\n{symptom_type}:')
        print(f'准确率: {results[symptom_type]["accuracy"]:.4f}')
        print(f'精确率: {results[symptom_type]["precision"]:.4f}')
        print(f'召回率: {results[symptom_type]["recall"]:.4f}')
        print(f'F1分数: {results[symptom_type]["f1"]:.4f}')
        print(f'AUC: {results[symptom_type]["auc"]:.4f}')
        print(f'阈值: {results[symptom_type]["threshold"]:.4f}')
        print(f'TP: {results[symptom_type]["tp"]}, FP: {results[symptom_type]["fp"]}, TN: {results[symptom_type]["tn"]}, FN: {results[symptom_type]["fn"]}')

    return results

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 准备数据
    train_loader, val_loader, test_loader, symptom_types = prepare_combined_data(
        data_dir=args.data_dir,
        label_file=args.label_file,
        max_seq_len=args.max_seq_len,
        batch_size=args.batch_size,
        augment_prob=args.augment_prob
    )

    if train_loader is None:
        print('数据准备失败')
        return

    # 创建模型
    model = TemporalEnhancedModel(
        visual_dim=128,  # 固定视觉特征维度
        audio_dim=128,   # 固定音频特征维度
        text_dim=768,    # BERT嵌入维度
        hidden_dim=args.hidden_dim,
        max_seq_len=args.max_seq_len
    ).to(args.device)

    # 创建损失函数
    criterion = nn.CrossEntropyLoss()

    # 创建优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay
    )

    # 创建学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5,
        verbose=True
    )

    # 训练模型
    print('开始训练...')
    train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        scheduler=scheduler,
        device=args.device,
        epochs=args.epochs,
        patience=args.patience,
        output_dir=args.output_dir
    )

    # 加载最佳模型
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'combined_model.pth')))

    # 评估模型
    print('开始评估...')
    evaluate_model(
        model=model,
        test_loader=test_loader,
        symptom_types=symptom_types,
        device=args.device,
        output_dir=args.output_dir
    )

    print(f'模型已保存到 {os.path.join(args.output_dir, "combined_model.pth")}')
    print(f'训练历史已保存到 {os.path.join(args.output_dir, "combined_training_history.json")}')
    print(f'评估指标已保存到 {os.path.join(args.output_dir, "combined_test_results.json")}')

if __name__ == '__main__':
    main()

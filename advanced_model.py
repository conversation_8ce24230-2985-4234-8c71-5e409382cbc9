import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    def __init__(self, embed_dim, num_heads):
        super(MultiHeadAttention, self).__init__()
        self.mha = nn.MultiheadAttention(embed_dim, num_heads, batch_first=True)
        
    def forward(self, query, key, value, attn_mask=None):
        attn_output, attn_weights = self.mha(query, key, value, attn_mask=attn_mask)
        return attn_output, attn_weights

class CrossModalAttention(nn.Module):
    """跨模态注意力机制"""
    def __init__(self, dim, num_heads=4):
        super(CrossModalAttention, self).__init__()
        self.dim = dim
        self.num_heads = num_heads
        
        # 自注意力层
        self.self_attention = MultiHeadAttention(dim, num_heads)
        
        # 跨模态注意力层
        self.cross_attention_v2a = MultiHeadAttention(dim, num_heads)  # 视觉对音频
        self.cross_attention_v2t = MultiHeadAttention(dim, num_heads)  # 视觉对文本
        self.cross_attention_a2v = MultiHeadAttention(dim, num_heads)  # 音频对视觉
        self.cross_attention_a2t = MultiHeadAttention(dim, num_heads)  # 音频对文本
        self.cross_attention_t2v = MultiHeadAttention(dim, num_heads)  # 文本对视觉
        self.cross_attention_t2a = MultiHeadAttention(dim, num_heads)  # 文本对音频
        
        # 输出层
        self.visual_output = nn.Sequential(
            nn.Linear(dim * 3, dim),
            nn.LayerNorm(dim),
            nn.ReLU()
        )
        
        self.audio_output = nn.Sequential(
            nn.Linear(dim * 3, dim),
            nn.LayerNorm(dim),
            nn.ReLU()
        )
        
        self.text_output = nn.Sequential(
            nn.Linear(dim * 3, dim),
            nn.LayerNorm(dim),
            nn.ReLU()
        )
        
    def forward(self, visual, audio, text):
        batch_size = visual.size(0)
        
        # 重塑输入以适应注意力机制 [batch_size, 1, dim]
        visual = visual.unsqueeze(1)
        audio = audio.unsqueeze(1)
        text = text.unsqueeze(1)
        
        # 自注意力
        visual_self, _ = self.self_attention(visual, visual, visual)
        audio_self, _ = self.self_attention(audio, audio, audio)
        text_self, _ = self.self_attention(text, text, text)
        
        # 跨模态注意力
        visual_audio, _ = self.cross_attention_v2a(visual, audio, audio)
        visual_text, _ = self.cross_attention_v2t(visual, text, text)
        
        audio_visual, _ = self.cross_attention_a2v(audio, visual, visual)
        audio_text, _ = self.cross_attention_a2t(audio, text, text)
        
        text_visual, _ = self.cross_attention_t2v(text, visual, visual)
        text_audio, _ = self.cross_attention_t2a(text, audio, audio)
        
        # 连接并处理
        visual_concat = torch.cat([visual_self, visual_audio, visual_text], dim=2)
        audio_concat = torch.cat([audio_self, audio_visual, audio_text], dim=2)
        text_concat = torch.cat([text_self, text_visual, text_audio], dim=2)
        
        visual_output = self.visual_output(visual_concat).squeeze(1)
        audio_output = self.audio_output(audio_concat).squeeze(1)
        text_output = self.text_output(text_concat).squeeze(1)
        
        return visual_output, audio_output, text_output

class GatedMultimodalFusion(nn.Module):
    """门控多模态融合"""
    def __init__(self, dim):
        super(GatedMultimodalFusion, self).__init__()
        self.dim = dim
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(dim * 3, dim * 3),
            nn.Sigmoid()
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(dim * 3, dim * 2),
            nn.LayerNorm(dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(dim * 2, dim),
            nn.LayerNorm(dim),
            nn.ReLU()
        )
        
    def forward(self, visual, audio, text):
        # 连接特征
        concat_features = torch.cat([visual, audio, text], dim=1)
        
        # 计算门控权重
        gate = self.gate_network(concat_features)
        
        # 应用门控
        gated_features = torch.cat([
            visual * gate[:, :self.dim],
            audio * gate[:, self.dim:2*self.dim],
            text * gate[:, 2*self.dim:3*self.dim]
        ], dim=1)
        
        # 融合特征
        fused = self.fusion_network(gated_features)
        
        return fused

class SymptomSpecificAttention(nn.Module):
    """症状特定注意力"""
    def __init__(self, dim, num_symptoms):
        super(SymptomSpecificAttention, self).__init__()
        self.dim = dim
        self.num_symptoms = num_symptoms
        
        # 症状特定查询向量
        self.symptom_queries = nn.Parameter(torch.randn(num_symptoms, dim))
        
        # 注意力层
        self.attention_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(dim, dim),
                nn.Tanh(),
                nn.Linear(dim, 1)
            ) for _ in range(num_symptoms)
        ])
        
    def forward(self, features):
        # features: [batch_size, dim]
        batch_size = features.size(0)
        
        # 计算每个症状的注意力权重
        symptom_features = []
        for i in range(self.num_symptoms):
            # 计算查询向量与特征的相似度
            query = self.symptom_queries[i].unsqueeze(0).expand(batch_size, -1)
            attention_input = features * query
            
            # 计算注意力权重
            attention_weights = self.attention_layers[i](attention_input)
            attention_weights = torch.sigmoid(attention_weights)
            
            # 应用注意力权重
            symptom_feature = features * attention_weights
            symptom_features.append(symptom_feature)
        
        return symptom_features

class AdvancedMultiSymptomModel(nn.Module):
    """高级多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, dropout_rate=0.3):
        super(AdvancedMultiSymptomModel, self).__init__()
        
        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # 跨模态注意力机制
        self.cross_modal_attention = CrossModalAttention(hidden_dim, num_heads=4)
        
        # 门控多模态融合
        self.gated_fusion = GatedMultimodalFusion(hidden_dim)
        
        # 症状特定注意力
        self.symptom_attention = SymptomSpecificAttention(hidden_dim, num_symptoms)
        
        # 多症状分类头
        self.symptom_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.LayerNorm(hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(hidden_dim // 2, 2)
            ) for _ in range(num_symptoms)
        ])
        
        # 残差连接
        self.residual_layer = nn.Linear(hidden_dim * 3, hidden_dim)
        
    def forward(self, visual, audio, text):
        # 编码特征
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 应用跨模态注意力
        visual_attended, audio_attended, text_attended = self.cross_modal_attention(
            visual_encoded, audio_encoded, text_encoded
        )
        
        # 门控融合
        fused = self.gated_fusion(visual_attended, audio_attended, text_attended)
        
        # 残差连接
        residual = self.residual_layer(torch.cat([visual_attended, audio_attended, text_attended], dim=1))
        fused = fused + residual
        
        # 症状特定注意力
        symptom_features = self.symptom_attention(fused)
        
        # 多症状分类
        outputs = [classifier(symptom_feature) for classifier, symptom_feature in zip(self.symptom_classifiers, symptom_features)]
        
        return outputs

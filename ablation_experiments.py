#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消融实验：评估时序增强和跨模态一致性方法的各个组件
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import json
import random
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
from ablation_temporal_model import AblationTemporalModel
from combined_temporal_consistency_processor import prepare_combined_data, set_seed
from sklearn.model_selection import train_test_split

# 定义消融实验配置
ABLATION_CONFIGS = [
    {
        'name': '完整模型',
        'description': '结合时序增强和跨模态一致性的完整模型',
        'use_temporal': True,
        'use_cross_modal': True,
        'use_adaptive_weight': True,
        'use_hierarchical': True
    },
    {
        'name': '仅时序增强',
        'description': '只使用时序增强，不使用跨模态一致性',
        'use_temporal': True,
        'use_cross_modal': False,
        'use_adaptive_weight': True,
        'use_hierarchical': True
    },
    {
        'name': '仅跨模态一致性',
        'description': '只使用跨模态一致性，不使用时序增强',
        'use_temporal': False,
        'use_cross_modal': True,
        'use_adaptive_weight': True,
        'use_hierarchical': True
    },
    {
        'name': '无自适应权重',
        'description': '不使用自适应权重模块',
        'use_temporal': True,
        'use_cross_modal': True,
        'use_adaptive_weight': False,
        'use_hierarchical': True
    },
    {
        'name': '无分层分类',
        'description': '不使用分层分类策略',
        'use_temporal': True,
        'use_cross_modal': True,
        'use_adaptive_weight': True,
        'use_hierarchical': False
    },
    {
        'name': '基础模型',
        'description': '不使用时序增强、跨模态一致性、自适应权重和分层分类',
        'use_temporal': False,
        'use_cross_modal': False,
        'use_adaptive_weight': False,
        'use_hierarchical': False
    },
    {
        'name': '时序+跨模态简化版',
        'description': '结合时序增强和跨模态一致性，但不使用自适应权重和分层分类',
        'use_temporal': True,
        'use_cross_modal': True,
        'use_adaptive_weight': False,
        'use_hierarchical': False
    }
]

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='消融实验：评估时序增强和跨模态一致性方法的各个组件')

    # 数据参数
    parser.add_argument('--data_dir', type=str, default='./shuju', help='特征数据目录')
    parser.add_argument('--label_file', type=str, default='./symptom_label_template.xlsx', help='标签文件路径')
    parser.add_argument('--output_dir', type=str, default='./ablation_results', help='输出目录')

    # 模型参数
    parser.add_argument('--max_seq_len', type=int, default=100, help='最大序列长度')
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--num_layers', type=int, default=2, help='LSTM层数')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout比例')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批量大小')
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.0005, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.0001, help='权重衰减')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--augment_prob', type=float, default=0.2, help='数据增强概率')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='设备')
    parser.add_argument('--config_index', type=int, default=None, help='要运行的特定配置索引，不指定则运行所有配置')

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 确定要运行的配置
    configs_to_run = [ABLATION_CONFIGS[args.config_index]] if args.config_index is not None else ABLATION_CONFIGS

    # 准备运行消融实验

    # 运行每个配置的消融实验
    all_results = {}

    for config in configs_to_run:
        print(f"\n{'='*50}")
        print(f"运行配置: {config['name']}")
        print(f"描述: {config['description']}")
        print(f"{'='*50}")

        # 为每个配置创建单独的输出目录
        config_output_dir = os.path.join(args.output_dir, config['name'].replace(' ', '_'))
        os.makedirs(config_output_dir, exist_ok=True)

        # 根据配置准备数据
        train_loader, val_loader, test_loader, _ = prepare_combined_data(
            data_dir=args.data_dir,
            label_file=args.label_file,
            max_seq_len=args.max_seq_len,
            batch_size=args.batch_size,
            augment_prob=args.augment_prob if (config['use_temporal'] or config['use_cross_modal']) else 0.0,
            use_temporal=config['use_temporal'],
            use_cross_modal=config['use_cross_modal']
        )

        if train_loader is None:
            print(f"配置 '{config['name']}' 的数据准备失败，跳过此配置")
            continue

        # 创建并训练模型
        model = create_and_train_model(
            train_loader,
            val_loader,
            args,
            config,
            config_output_dir
        )

        # 评估模型并保存结果
        results = evaluate_model(
            model,
            test_loader,
            args.device,
            config_output_dir
        )

        # 保存此配置的结果
        all_results[config['name']] = results

    # 保存所有配置的汇总结果
    with open(os.path.join(args.output_dir, 'all_ablation_results.json'), 'w') as f:
        json.dump(all_results, f, indent=4)

    # 生成比较报告
    generate_comparison_report(all_results, args.output_dir)

    print("\n所有消融实验完成！")



def create_and_train_model(train_loader, val_loader, args, config, output_dir):
    """
    创建并训练模型

    参数:
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - symptom_types: 症状类型列表
    - args: 命令行参数
    - config: 消融实验配置
    - output_dir: 输出目录

    返回:
    - 训练好的模型
    """
    # 创建模型
    model = AblationTemporalModel(
        hidden_dim=args.hidden_dim,
        max_seq_len=args.max_seq_len,
        use_temporal=config['use_temporal'],
        use_cross_modal=config['use_cross_modal'],
        use_adaptive_weight=config['use_adaptive_weight'],
        use_hierarchical=config['use_hierarchical']
    ).to(args.device)

    # 创建损失函数
    criterion = nn.CrossEntropyLoss()

    # 创建优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay
    )

    # 创建学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5
    )

    # 训练模型
    print(f"开始训练 {config['name']} 模型...")
    train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        scheduler=scheduler,
        device=args.device,
        epochs=args.epochs,
        patience=args.patience,
        output_dir=output_dir
    )

    # 加载最佳模型
    model.load_state_dict(torch.load(os.path.join(output_dir, 'best_model.pth')))

    return model

def train_model(model, train_loader, val_loader, criterion, optimizer, scheduler, device, epochs, patience, output_dir):
    """
    训练模型

    参数:
    - model: 模型
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - criterion: 损失函数
    - optimizer: 优化器
    - scheduler: 学习率调度器
    - device: 设备
    - epochs: 训练轮数
    - patience: 早停耐心值
    - output_dir: 输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 初始化训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'val_accuracy': []
    }

    # 初始化早停变量
    best_val_loss = float('inf')
    best_model_state = None
    patience_counter = 0

    # 训练循环
    for epoch in range(epochs):
        # 训练模式
        model.train()
        train_loss = 0.0

        for batch_idx, (visual_batch, audio_batch, text_batch, labels_batch) in enumerate(train_loader):
            # 将数据移动到设备
            visual_batch = visual_batch.to(device)
            audio_batch = audio_batch.to(device)
            text_batch = text_batch.to(device)

            # 清零梯度
            optimizer.zero_grad()

            # 打印输入维度
            if batch_idx == 0:
                print(f"Visual batch shape: {visual_batch.shape}")
                print(f"Audio batch shape: {audio_batch.shape}")
                print(f"Text batch shape: {text_batch.shape}")

            # 前向传播
            outputs = model(visual_batch, audio_batch, text_batch)

            # 计算损失
            loss = 0.0
            for i, symptom_output in enumerate(outputs):
                symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)
                loss += criterion(symptom_output, symptom_labels)

            # 反向传播
            loss.backward()

            # 更新参数
            optimizer.step()

            # 累加损失
            train_loss += loss.item()

            # 打印进度
            if (batch_idx + 1) % 10 == 0:
                print(f'Epoch {epoch+1}/{epochs} | Batch {batch_idx+1}/{len(train_loader)} | Loss: {loss.item():.4f}')

        # 计算平均训练损失
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)

        # 验证模式
        model.eval()
        val_loss = 0.0
        all_preds = [[] for _ in range(len(outputs))]
        all_labels = [[] for _ in range(len(outputs))]

        with torch.no_grad():
            for visual_batch, audio_batch, text_batch, labels_batch in val_loader:
                # 将数据移动到设备
                visual_batch = visual_batch.to(device)
                audio_batch = audio_batch.to(device)
                text_batch = text_batch.to(device)

                # 前向传播
                outputs = model(visual_batch, audio_batch, text_batch)

                # 计算损失
                batch_loss = 0.0
                for i, symptom_output in enumerate(outputs):
                    symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)
                    batch_loss += criterion(symptom_output, symptom_labels)

                    # 收集预测和标签
                    preds = torch.argmax(symptom_output, dim=1).cpu().numpy()
                    labels = symptom_labels.cpu().numpy()
                    all_preds[i].extend(preds)
                    all_labels[i].extend(labels)

                # 累加损失
                val_loss += batch_loss.item()

        # 计算平均验证损失
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)

        # 计算平均准确率
        accuracies = []
        for i in range(len(all_preds)):
            accuracy = accuracy_score(all_labels[i], all_preds[i])
            accuracies.append(accuracy)

        avg_accuracy = sum(accuracies) / len(accuracies)
        history['val_accuracy'].append(avg_accuracy)

        # 打印验证结果
        print(f'Epoch {epoch+1}/{epochs} | Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f} | Val Accuracy: {avg_accuracy:.4f}')

        # 学习率调度
        scheduler.step(val_loss)

        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict()
            patience_counter = 0

            # 保存最佳模型
            torch.save(best_model_state, os.path.join(output_dir, 'best_model.pth'))
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f'早停: {patience} 轮验证损失未改善')
                break

    # 保存训练历史
    with open(os.path.join(output_dir, 'training_history.json'), 'w') as f:
        json.dump(history, f, indent=4)

    # 恢复最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    return model

def evaluate_model(model, test_loader, device, output_dir):
    """
    评估模型

    参数:
    - model: 模型
    - test_loader: 测试数据加载器
    - device: 设备
    - output_dir: 输出目录

    返回:
    - 评估结果
    """
    # 定义症状类型
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']

    # 评估模式
    model.eval()

    # 收集预测和标签
    all_outputs = [[] for _ in range(len(symptom_types))]
    all_labels = [[] for _ in range(len(symptom_types))]

    with torch.no_grad():
        for visual_batch, audio_batch, text_batch, labels_batch in test_loader:
            # 将数据移动到设备
            visual_batch = visual_batch.to(device)
            audio_batch = audio_batch.to(device)
            text_batch = text_batch.to(device)

            # 前向传播
            outputs = model(visual_batch, audio_batch, text_batch, training=False)

            # 收集输出和标签
            for i, symptom_output in enumerate(outputs):
                symptom_labels = torch.stack([labels[i] for labels in labels_batch]).to(device)

                # 如果输出是logits，转换为概率
                if isinstance(symptom_output, torch.Tensor) and symptom_output.size(1) == 2:
                    # 保存logits用于后续计算
                    all_outputs[i].extend(symptom_output.cpu().numpy())
                else:
                    # 如果已经是概率，直接保存
                    all_outputs[i].extend(symptom_output.cpu().numpy())

                all_labels[i].extend(symptom_labels.cpu().numpy())

    # 计算最佳阈值和评估指标
    results = {}
    for i, symptom_type in enumerate(symptom_types):
        # 转换为numpy数组
        outputs = np.array(all_outputs[i])
        labels = np.array(all_labels[i])

        # 计算预测概率
        if outputs.shape[1] == 2:  # 如果是logits
            probs = np.exp(outputs) / np.sum(np.exp(outputs), axis=1, keepdims=True)
            positive_probs = probs[:, 1]  # 正类的概率
        else:  # 如果已经是概率
            positive_probs = outputs.flatten()

        # 寻找最佳阈值
        best_threshold = 0.5
        best_f1 = 0.0

        for threshold in np.arange(0.1, 0.9, 0.01):
            preds = (positive_probs >= threshold).astype(int)
            f1 = f1_score(labels, preds)

            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

        # 使用最佳阈值进行预测
        preds = (positive_probs >= best_threshold).astype(int)

        # 计算评估指标
        accuracy = accuracy_score(labels, preds)
        precision = precision_score(labels, preds)
        recall = recall_score(labels, preds)
        f1 = f1_score(labels, preds)
        auc = roc_auc_score(labels, positive_probs)

        # 计算混淆矩阵
        tn, fp, fn, tp = confusion_matrix(labels, preds).ravel()

        # 保存结果
        results[symptom_type] = {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1': float(f1),
            'auc': float(auc),
            'threshold': float(best_threshold),
            'tp': int(tp),
            'fp': int(fp),
            'tn': int(tn),
            'fn': int(fn)
        }

    # 计算平均指标
    avg_accuracy = np.mean([results[symptom_type]['accuracy'] for symptom_type in symptom_types])
    avg_precision = np.mean([results[symptom_type]['precision'] for symptom_type in symptom_types])
    avg_recall = np.mean([results[symptom_type]['recall'] for symptom_type in symptom_types])
    avg_f1 = np.mean([results[symptom_type]['f1'] for symptom_type in symptom_types])
    avg_auc = np.mean([results[symptom_type]['auc'] for symptom_type in symptom_types])

    results['average'] = {
        'accuracy': float(avg_accuracy),
        'precision': float(avg_precision),
        'recall': float(avg_recall),
        'f1': float(avg_f1),
        'auc': float(avg_auc)
    }

    # 保存评估结果
    with open(os.path.join(output_dir, 'test_results.json'), 'w') as f:
        json.dump(results, f, indent=4)

    print(f"评估结果已保存到 {os.path.join(output_dir, 'test_results.json')}")

    return results

def generate_comparison_report(all_results, output_dir):
    """
    生成比较报告

    参数:
    - all_results: 所有配置的结果
    - output_dir: 输出目录
    """
    # 创建比较表格
    comparison = {
        'config': [],
        'accuracy': [],
        'precision': [],
        'recall': [],
        'f1': [],
        'auc': []
    }

    # 收集每个配置的平均指标
    for config_name, results in all_results.items():
        if 'average' in results:
            comparison['config'].append(config_name)
            comparison['accuracy'].append(results['average']['accuracy'])
            comparison['precision'].append(results['average']['precision'])
            comparison['recall'].append(results['average']['recall'])
            comparison['f1'].append(results['average']['f1'])
            comparison['auc'].append(results['average']['auc'])

    # 创建详细的比较报告
    report = "# 消融实验比较报告\n\n"

    # 添加总体比较表格
    report += "## 总体性能比较\n\n"
    report += "| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |\n"
    report += "| ---- | ------ | ------ | ------ | ------ | --- |\n"

    for i in range(len(comparison['config'])):
        report += f"| {comparison['config'][i]} | {comparison['accuracy'][i]:.4f} | {comparison['precision'][i]:.4f} | {comparison['recall'][i]:.4f} | {comparison['f1'][i]:.4f} | {comparison['auc'][i]:.4f} |\n"

    # 添加各症状类型的比较
    report += "\n## 各症状类型性能比较\n\n"

    symptom_types = next(iter(all_results.values())).keys()
    symptom_types = [st for st in symptom_types if st != 'average']

    for symptom_type in symptom_types:
        report += f"### {symptom_type}\n\n"
        report += "| 配置 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |\n"
        report += "| ---- | ------ | ------ | ------ | ------ | --- |\n"

        for config_name, results in all_results.items():
            if symptom_type in results:
                report += f"| {config_name} | {results[symptom_type]['accuracy']:.4f} | {results[symptom_type]['precision']:.4f} | {results[symptom_type]['recall']:.4f} | {results[symptom_type]['f1']:.4f} | {results[symptom_type]['auc']:.4f} |\n"

        report += "\n"

    # 添加结论
    report += "## 结论\n\n"

    # 找出F1分数最高的配置
    best_config_idx = np.argmax(comparison['f1'])
    best_config = comparison['config'][best_config_idx]

    report += f"1. 最佳配置: **{best_config}** (F1分数: {comparison['f1'][best_config_idx]:.4f})\n"

    # 比较完整模型与其他配置
    if '完整模型' in comparison['config']:
        full_model_idx = comparison['config'].index('完整模型')
        full_model_f1 = comparison['f1'][full_model_idx]

        report += "2. 各组件贡献:\n"

        for i, config in enumerate(comparison['config']):
            if config != '完整模型':
                f1_diff = full_model_f1 - comparison['f1'][i]
                report += f"   - **{config}**: 比完整模型低 {f1_diff:.4f} (F1分数)\n"

    # 保存比较报告
    with open(os.path.join(output_dir, 'comparison_report.md'), 'w') as f:
        f.write(report)

    print(f"比较报告已保存到 {os.path.join(output_dir, 'comparison_report.md')}")

if __name__ == "__main__":
    main()

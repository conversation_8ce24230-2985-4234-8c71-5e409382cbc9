import os
import json
import pandas as pd
import numpy as np
import argparse

def load_results(results_dir='ablation_results'):
    """加载所有模型的结果"""
    all_results = {}
    
    # 检查目录是否存在
    if not os.path.exists(results_dir):
        print(f"结果目录 {results_dir} 不存在！")
        return all_results
    
    # 加载单个模型结果文件
    for filename in os.listdir(results_dir):
        if filename.endswith('_results.json'):
            model_name = filename.replace('_results.json', '')
            
            # 跳过all_model_results.json
            if model_name == 'all_model':
                continue
                
            file_path = os.path.join(results_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    all_results[model_name] = results
            except Exception as e:
                print(f"加载 {file_path} 时出错: {e}")
    
    # 如果没有找到单个模型结果，尝试加载合并结果文件
    if not all_results and os.path.exists(os.path.join(results_dir, 'all_model_results.json')):
        try:
            with open(os.path.join(results_dir, 'all_model_results.json'), 'r', encoding='utf-8') as f:
                all_results = json.load(f)
        except Exception as e:
            print(f"加载合并结果文件时出错: {e}")
    
    return all_results

def create_comparison_tables(all_results, symptom_types=['情绪症状', '躯体症状', '认知症状', '行为症状']):
    """创建比较表格"""
    # 总体性能比较表
    overall_table = {
        '配置': [],
        '准确率': [],
        '精确率': [],
        '召回率': [],
        'F1分数': [],
        'AUC': []
    }
    
    # 各症状类型性能比较表
    symptom_tables = {symptom: {
        '配置': [],
        '准确率': [],
        '精确率': [],
        '召回率': [],
        'F1分数': [],
        'AUC': []
    } for symptom in symptom_types}
    
    # 模型名称映射
    model_name_map = {
        'self_attention': 'Self-attention',
        'cross_attention': 'Cross-attention',
        'cube_mlp': 'CubeMLP',
        'gru_bilstm': 'GRU-BiLSTM',
        'bilstm': 'Bi-LSTM',
        'mult': 'MulT',
        'pmr': 'PMR'
    }
    
    # 填充表格
    for model_name, results in all_results.items():
        # 使用映射的模型名称
        display_name = model_name_map.get(model_name, model_name)
        
        # 总体性能
        overall_table['配置'].append(display_name)
        overall_table['准确率'].append(results['overall']['accuracy'])
        overall_table['精确率'].append(results['overall']['precision'])
        overall_table['召回率'].append(results['overall']['recall'])
        overall_table['F1分数'].append(results['overall']['f1'])
        overall_table['AUC'].append(results['overall']['auc'])
        
        # 各症状类型性能
        for symptom in symptom_types:
            if symptom in results:
                symptom_tables[symptom]['配置'].append(display_name)
                symptom_tables[symptom]['准确率'].append(results[symptom]['accuracy'])
                symptom_tables[symptom]['精确率'].append(results[symptom]['precision'])
                symptom_tables[symptom]['召回率'].append(results[symptom]['recall'])
                symptom_tables[symptom]['F1分数'].append(results[symptom]['f1'])
                symptom_tables[symptom]['AUC'].append(results[symptom]['auc'])
    
    # 转换为DataFrame
    overall_df = pd.DataFrame(overall_table)
    symptom_dfs = {symptom: pd.DataFrame(table) for symptom, table in symptom_tables.items()}
    
    return overall_df, symptom_dfs

def generate_markdown_report(overall_df, symptom_dfs, output_file='ablation_results/model_variants_comparison.md'):
    """生成Markdown格式的报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        # 标题
        f.write("# 模型变体比较报告\n\n")
        
        # 总体性能比较
        f.write("## 总体性能比较\n\n")
        f.write(overall_df.to_markdown(index=False, floatfmt=".4f"))
        f.write("\n\n")
        
        # 各症状类型性能比较
        f.write("## 各症状类型性能比较\n\n")
        
        for symptom, df in symptom_dfs.items():
            f.write(f"### {symptom}\n\n")
            f.write(df.to_markdown(index=False, floatfmt=".4f"))
            f.write("\n\n")
        
        # 结论
        f.write("## 结论\n\n")
        
        # 找出总体性能最好的模型
        best_overall_f1_idx = overall_df['F1分数'].idxmax()
        best_overall_model = overall_df.iloc[best_overall_f1_idx]['配置']
        best_overall_f1 = overall_df.iloc[best_overall_f1_idx]['F1分数']
        
        # 找出各症状性能最好的模型
        best_symptom_models = {}
        for symptom, df in symptom_dfs.items():
            best_idx = df['F1分数'].idxmax()
            best_symptom_models[symptom] = {
                'model': df.iloc[best_idx]['配置'],
                'f1': df.iloc[best_idx]['F1分数']
            }
        
        # 写入结论
        f.write(f"1. **最佳整体性能**:\n")
        f.write(f"   - {best_overall_model} 模型在总体性能上表现最好，F1分数为{best_overall_f1:.4f}。\n\n")
        
        f.write(f"2. **各症状类型最佳模型**:\n")
        for symptom, best in best_symptom_models.items():
            f.write(f"   - **{symptom}**: {best['model']} 模型表现最好，F1分数为{best['f1']:.4f}。\n")
        f.write("\n")
        
        # 模型特点分析
        f.write(f"3. **模型特点分析**:\n")
        f.write(f"   - **Self-attention**: 通过自注意力机制处理单一模态内的特征关系，能够捕捉模态内部的依赖关系。\n")
        f.write(f"   - **Cross-attention**: 通过跨模态注意力机制处理不同模态之间的特征交互，有助于捕捉模态间的互补信息。\n")
        f.write(f"   - **CubeMLP**: 使用立方体MLP结构处理多维特征，能够在不同维度上提取特征。\n")
        f.write(f"   - **GRU-BiLSTM**: 结合GRU和BiLSTM的优势，先使用GRU处理序列，再使用BiLSTM进一步提取特征，适合处理时序数据。\n")
        f.write(f"   - **Bi-LSTM**: 使用双向LSTM捕捉序列的前向和后向依赖关系，适合处理时序数据。\n")
        f.write(f"   - **MulT**: 多模态Transformer模型，通过跨模态注意力机制融合不同模态的信息，能够处理模态间的复杂交互。\n")
        f.write(f"   - **PMR**: 渐进式多模态推理模型，通过多步推理过程逐步融合和推理多模态信息，适合处理复杂的多模态任务。\n\n")
        
        # 建议
        f.write(f"4. **建议**:\n")
        f.write(f"   - 对于实际应用，可以考虑使用 {best_overall_model} 模型，它提供了最佳的整体性能。\n")
        f.write(f"   - 对于特定症状类型的检测，可以考虑使用针对该症状表现最好的模型配置。\n")
        f.write(f"   - 可以尝试集成多个模型的预测结果，以获得更稳健的性能。\n")
        f.write(f"   - 进一步研究可以探索如何结合不同模型的优势，例如结合时序模型（GRU-BiLSTM、Bi-LSTM）和注意力模型（Self-attention、Cross-attention）的特点。\n")

def update_comprehensive_report(model_variants_file, comprehensive_file='ablation_results/comprehensive_comparison_report.md'):
    """更新综合比较报告，添加新的模型变体结果"""
    # 检查文件是否存在
    if not os.path.exists(model_variants_file):
        print(f"模型变体比较文件 {model_variants_file} 不存在！")
        return
    
    if not os.path.exists(comprehensive_file):
        print(f"综合比较报告文件 {comprehensive_file} 不存在！")
        return
    
    # 读取模型变体比较文件
    with open(model_variants_file, 'r', encoding='utf-8') as f:
        model_variants_content = f.read()
    
    # 读取综合比较报告文件
    with open(comprehensive_file, 'r', encoding='utf-8') as f:
        comprehensive_content = f.read()
    
    # 提取模型变体比较中的表格
    import re
    
    # 提取总体性能比较表格
    overall_table_match = re.search(r'## 总体性能比较\n\n(.*?)\n\n', model_variants_content, re.DOTALL)
    if overall_table_match:
        overall_table = overall_table_match.group(1)
    else:
        print("无法提取总体性能比较表格！")
        return
    
    # 提取各症状类型性能比较表格
    symptom_tables = {}
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']
    
    for symptom in symptom_types:
        symptom_table_match = re.search(f'### {symptom}\n\n(.*?)\n\n', model_variants_content, re.DOTALL)
        if symptom_table_match:
            symptom_tables[symptom] = symptom_table_match.group(1)
        else:
            print(f"无法提取 {symptom} 性能比较表格！")
    
    # 更新综合比较报告中的表格
    # 更新总体性能比较表格
    comprehensive_content = re.sub(
        r'## 总体性能比较\n\n(.*?)\n\n',
        f'## 总体性能比较\n\n{overall_table}\n\n',
        comprehensive_content,
        flags=re.DOTALL
    )
    
    # 更新各症状类型性能比较表格
    for symptom, table in symptom_tables.items():
        comprehensive_content = re.sub(
            f'### {symptom}\n\n(.*?)\n\n',
            f'### {symptom}\n\n{table}\n\n',
            comprehensive_content,
            flags=re.DOTALL
        )
    
    # 更新结论部分
    # 提取模型变体比较中的结论
    conclusion_match = re.search(r'## 结论\n\n(.*?)$', model_variants_content, re.DOTALL)
    if conclusion_match:
        model_variants_conclusion = conclusion_match.group(1)
        
        # 更新综合比较报告中的结论
        comprehensive_content = re.sub(
            r'## 结论\n\n(.*?)$',
            f'## 结论\n\n{model_variants_conclusion}',
            comprehensive_content,
            flags=re.DOTALL
        )
    else:
        print("无法提取模型变体比较中的结论！")
    
    # 保存更新后的综合比较报告
    with open(comprehensive_file, 'w', encoding='utf-8') as f:
        f.write(comprehensive_content)
    
    print(f"已更新综合比较报告 {comprehensive_file}")

def main():
    parser = argparse.ArgumentParser(description='比较不同模型变体的结果')
    parser.add_argument('--results_dir', type=str, default='ablation_results', help='结果目录')
    parser.add_argument('--output_file', type=str, default='ablation_results/model_variants_comparison.md', help='输出文件路径')
    parser.add_argument('--update_comprehensive', action='store_true', help='是否更新综合比较报告')
    parser.add_argument('--comprehensive_file', type=str, default='ablation_results/comprehensive_comparison_report.md', help='综合比较报告文件路径')
    
    args = parser.parse_args()
    
    # 加载结果
    all_results = load_results(args.results_dir)
    
    if not all_results:
        print("没有找到任何结果！")
        return
    
    # 创建比较表格
    overall_df, symptom_dfs = create_comparison_tables(all_results)
    
    # 生成Markdown报告
    generate_markdown_report(overall_df, symptom_dfs, args.output_file)
    
    print(f"比较报告已生成: {args.output_file}")
    
    # 更新综合比较报告
    if args.update_comprehensive:
        update_comprehensive_report(args.output_file, args.comprehensive_file)

if __name__ == '__main__':
    main()

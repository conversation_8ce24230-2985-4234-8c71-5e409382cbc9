import os
import torch
import numpy as np
import argparse
from hierarchical_model import HierarchicalSymptomModel, HierarchicalSymptomLoss
from temporal_data_processor import prepare_temporal_data
from temporal_trainer import train_temporal_model, evaluate_temporal_model, save_results, calculate_class_weights

def set_seed(seed):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def train_hierarchical_model(model, train_loader, val_loader, symptom_types, device, 
                            learning_rate=0.0005, weight_decay=1e-5, epochs=100,
                            patience=15, min_delta=0.001,
                            class_weight=None, focal_loss_gamma=2.0):
    """
    训练分层症状识别模型
    
    参数:
    - model: 模型实例
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - symptom_types: 症状类型列表
    - device: 训练设备
    - learning_rate: 初始学习率
    - weight_decay: 权重衰减系数
    - epochs: 最大训练轮数
    - patience: 早停耐心值
    - min_delta: 早停最小改进阈值
    - class_weight: 类别权重，用于处理类别不平衡
    - focal_loss_gamma: Focal Loss的gamma参数
    
    返回:
    - 训练后的模型
    - 训练历史记录
    """
    # 使用AdamW优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    
    # 学习率调度器 - 余弦退火带热重启
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=learning_rate/100
    )
    
    # 为每个症状类型计算类别权重
    if class_weight is None:
        class_weights = calculate_class_weights(train_loader, symptom_types, device)
    else:
        class_weights = class_weight
    
    # 创建分层损失函数
    hierarchical_losses = {
        symptom: HierarchicalSymptomLoss(presence_weight=0.3, type_weight=0.7)
        for symptom in symptom_types
    }
    
    # 用于记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rates': []
    }
    
    # 为每种症状添加指标记录
    for symptom in symptom_types:
        history[f'{symptom}_val_acc'] = []
        history[f'{symptom}_val_precision'] = []
        history[f'{symptom}_val_recall'] = []
        history[f'{symptom}_val_f1'] = []
        history[f'{symptom}_val_auc'] = []
        history[f'{symptom}_val_threshold'] = []
    
    # 早停设置
    best_val_loss = float('inf')
    best_model_state = None
    best_epoch = 0
    no_improve_count = 0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for visual, audio, text, labels in train_loader:
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)
            labels = [label.to(device) for label in labels]  # 每个症状的标签
            
            # 前向传播
            outputs = model(visual, audio, text, training=True)
            
            # 计算每个症状的损失并求和
            loss = 0
            for i, (output, label) in enumerate(zip(outputs, labels)):
                symptom = symptom_types[i]
                # 使用分层损失
                loss += hierarchical_losses[symptom](output, label)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        history['learning_rates'].append(optimizer.param_groups[0]['lr'])
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        all_preds = {symptom: [] for symptom in symptom_types}
        all_labels = {symptom: [] for symptom in symptom_types}
        all_probs = {symptom: [] for symptom in symptom_types}
        
        with torch.no_grad():
            for visual, audio, text, labels in val_loader:
                visual, audio, text = visual.to(device), audio.to(device), text.to(device)
                target_labels = [label.to(device) for label in labels]
                
                # 前向传播 - 推理模式
                outputs = model(visual, audio, text, training=False)
                
                # 计算损失和收集预测
                batch_loss = 0
                for i, symptom in enumerate(symptom_types):
                    # 获取预测概率
                    probs = outputs[i].cpu().numpy()
                    
                    # 使用0.5作为初始阈值
                    preds = (probs > 0.5).astype(int)
                    true_labels = target_labels[i].cpu().numpy()
                    
                    all_probs[symptom].extend(probs)
                    all_preds[symptom].extend(preds)
                    all_labels[symptom].extend(true_labels)
                    
                    # 使用二元交叉熵损失
                    criterion = torch.nn.BCELoss()
                    batch_loss += criterion(outputs[i], target_labels[i].float().unsqueeze(1))
                
                val_loss += batch_loss.item()
        
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # 更新学习率
        scheduler.step()
        
        # 计算每个症状的指标
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_curve, auc, precision_recall_curve
        
        print(f"Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        for symptom in symptom_types:
            # 优化阈值
            precision, recall, thresholds = precision_recall_curve(
                all_labels[symptom], all_probs[symptom]
            )
            
            # 计算F1分数
            f1_scores = 2 * precision * recall / (precision + recall + 1e-10)
            
            # 找到最佳阈值
            best_idx = np.argmax(f1_scores)
            best_threshold = thresholds[best_idx] if best_idx < len(thresholds) else 0.5
            
            # 使用最佳阈值重新计算预测
            optimized_preds = (np.array(all_probs[symptom]) > best_threshold).astype(int)
            
            # 计算指标
            acc = accuracy_score(all_labels[symptom], optimized_preds)
            prec = precision_score(all_labels[symptom], optimized_preds, zero_division=0)
            rec = recall_score(all_labels[symptom], optimized_preds, zero_division=0)
            f1 = f1_score(all_labels[symptom], optimized_preds, zero_division=0)
            
            # 计算AUC
            fpr, tpr, _ = roc_curve(all_labels[symptom], all_probs[symptom])
            roc_auc = auc(fpr, tpr)
            
            history[f'{symptom}_val_acc'].append(acc)
            history[f'{symptom}_val_precision'].append(prec)
            history[f'{symptom}_val_recall'].append(rec)
            history[f'{symptom}_val_f1'].append(f1)
            history[f'{symptom}_val_auc'].append(roc_auc)
            history[f'{symptom}_val_threshold'].append(best_threshold)
            
            print(f"  {symptom} - Acc: {acc:.4f}, Precision: {prec:.4f}, Recall: {rec:.4f}, F1: {f1:.4f}, AUC: {roc_auc:.4f}, Threshold: {best_threshold:.4f}")
        
        # 检查是否是最佳模型
        if val_loss < best_val_loss - min_delta:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            best_epoch = epoch
            no_improve_count = 0
            print(f"发现新的最佳模型，验证损失: {val_loss:.4f}")
        else:
            no_improve_count += 1
            print(f"验证损失未改善，已经 {no_improve_count} 轮未改善")
        
        # 早停检查
        if no_improve_count >= patience:
            print(f"早停触发！已经 {patience} 轮验证损失未改善")
            break
    
    # 恢复最佳模型
    print(f"使用第 {best_epoch+1} 轮的最佳模型，验证损失: {best_val_loss:.4f}")
    model.load_state_dict(best_model_state)
    
    return model, history

def evaluate_hierarchical_model(model, test_loader, symptom_types, device, 
                               custom_thresholds=None, optimize_thresholds=True):
    """
    评估分层症状识别模型
    
    参数:
    - model: 模型实例
    - test_loader: 测试数据加载器
    - symptom_types: 症状类型列表
    - device: 训练设备
    - custom_thresholds: 自定义决策阈值字典 {symptom: threshold}
    - optimize_thresholds: 是否优化决策阈值
    
    返回:
    - 每个症状的评估指标
    """
    model.eval()
    all_preds = {symptom: [] for symptom in symptom_types}
    all_labels = {symptom: [] for symptom in symptom_types}
    all_probs = {symptom: [] for symptom in symptom_types}
    
    with torch.no_grad():
        for visual, audio, text, labels in test_loader:
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)
            target_labels = [label.to(device) for label in labels]
            
            # 前向传播 - 推理模式
            outputs = model(visual, audio, text, training=False)
            
            # 收集预测和标签
            for i, symptom in enumerate(symptom_types):
                probs = outputs[i].cpu().numpy()
                true_labels = target_labels[i].cpu().numpy()
                
                all_probs[symptom].extend(probs)
                all_labels[symptom].extend(true_labels)
    
    # 如果需要优化阈值且没有提供自定义阈值
    if optimize_thresholds and custom_thresholds is None:
        custom_thresholds = {}
        
        for symptom in symptom_types:
            # 优化阈值
            from sklearn.metrics import precision_recall_curve
            precision, recall, thresholds = precision_recall_curve(
                all_labels[symptom], all_probs[symptom]
            )
            
            # 计算F1分数
            f1_scores = 2 * precision * recall / (precision + recall + 1e-10)
            
            # 找到最佳阈值
            best_idx = np.argmax(f1_scores)
            best_threshold = thresholds[best_idx] if best_idx < len(thresholds) else 0.5
            
            custom_thresholds[symptom] = best_threshold
            print(f"为 {symptom} 优化的阈值: {best_threshold:.4f}")
    
    # 使用阈值进行预测
    for symptom in symptom_types:
        threshold = custom_thresholds.get(symptom, 0.5) if custom_thresholds else 0.5
        all_preds[symptom] = (np.array(all_probs[symptom]) > threshold).astype(int)
    
    # 计算每个症状的指标
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_curve, auc
    
    metrics = {}
    
    for symptom in symptom_types:
        true_labels = all_labels[symptom]
        preds = all_preds[symptom]
        probs = all_probs[symptom]
        
        # 计算基本指标
        accuracy = accuracy_score(true_labels, preds)
        precision = precision_score(true_labels, preds, zero_division=0)
        recall = recall_score(true_labels, preds, zero_division=0)
        f1 = f1_score(true_labels, preds, zero_division=0)
        
        # 计算ROC曲线和AUC
        fpr, tpr, _ = roc_curve(true_labels, probs)
        roc_auc = auc(fpr, tpr)
        
        # 计算混淆矩阵元素
        tp = np.sum((np.array(preds) == 1) & (np.array(true_labels) == 1))
        fp = np.sum((np.array(preds) == 1) & (np.array(true_labels) == 0))
        tn = np.sum((np.array(preds) == 0) & (np.array(true_labels) == 0))
        fn = np.sum((np.array(preds) == 0) & (np.array(true_labels) == 1))
        
        metrics[symptom] = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': roc_auc,
            'threshold': custom_thresholds.get(symptom, 0.5) if custom_thresholds else 0.5,
            'tp': int(tp),
            'fp': int(fp),
            'tn': int(tn),
            'fn': int(fn)
        }
        
        print(f"{symptom} 测试结果:")
        print(f"  准确率: {accuracy:.4f}")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  F1分数: {f1:.4f}")
        print(f"  AUC: {roc_auc:.4f}")
        print(f"  阈值: {metrics[symptom]['threshold']:.4f}")
        print(f"  混淆矩阵: TP={tp}, FP={fp}, TN={tn}, FN={fn}")
    
    # 计算平均指标
    avg_accuracy = sum(metrics[symptom]['accuracy'] for symptom in symptom_types) / len(symptom_types)
    avg_precision = sum(metrics[symptom]['precision'] for symptom in symptom_types) / len(symptom_types)
    avg_recall = sum(metrics[symptom]['recall'] for symptom in symptom_types) / len(symptom_types)
    avg_f1 = sum(metrics[symptom]['f1'] for symptom in symptom_types) / len(symptom_types)
    avg_auc = sum(metrics[symptom]['auc'] for symptom in symptom_types) / len(symptom_types)
    
    print(f"\n平均指标:")
    print(f"  平均准确率: {avg_accuracy:.4f}")
    print(f"  平均精确率: {avg_precision:.4f}")
    print(f"  平均召回率: {avg_recall:.4f}")
    print(f"  平均F1分数: {avg_f1:.4f}")
    print(f"  平均AUC: {avg_auc:.4f}")
    
    # 添加平均指标到结果中
    metrics['average'] = {
        'accuracy': avg_accuracy,
        'precision': avg_precision,
        'recall': avg_recall,
        'f1': avg_f1,
        'auc': avg_auc
    }
    
    return metrics

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="分层症状识别模型")
    parser.add_argument("--data_dir", type=str, default="./shuju", help="特征数据目录")
    parser.add_argument("--label_file", type=str, default="./symptom_label_template.xlsx", help="标签文件路径")
    
    # 学术规范的训练参数
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.0005, help="初始学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-5, help="权重衰减系数")
    parser.add_argument("--patience", type=int, default=15, help="早停耐心值")
    parser.add_argument("--min_delta", type=float, default=0.001, help="早停最小改进阈值")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    
    # 模型参数
    parser.add_argument("--hidden_dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--augment_prob", type=float, default=0.2, help="数据增强概率")
    
    # 损失函数参数
    parser.add_argument("--focal_loss_gamma", type=float, default=2.0, help="Focal Loss的gamma参数")
    
    # 其他参数
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="训练设备")
    parser.add_argument("--output_dir", type=str, default="./hierarchical_results", help="输出目录")
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 要预测的症状列表
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']
    
    # 准备数据
    print("准备数据...")
    train_loader, val_loader, test_loader, symptom_types = prepare_temporal_data(
        data_dir=args.data_dir,
        label_file=args.label_file,
        symptom_types=symptom_types,
        batch_size=args.batch_size,
        augment_prob=args.augment_prob,
        seed=args.seed
    )
    
    # 创建分层症状识别模型
    print("创建分层症状识别模型...")
    model = HierarchicalSymptomModel(
        visual_dim=128,  # 根据实际特征维度调整
        audio_dim=128,   # 根据实际特征维度调整
        text_dim=768,    # BERT嵌入维度
        hidden_dim=args.hidden_dim
    )
    
    model.to(device)
    print("模型已创建并移动到设备上")
    
    # 训练模型
    print("\n开始训练分层症状识别模型...")
    model, training_history = train_hierarchical_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        symptom_types=symptom_types,
        device=device,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        epochs=args.epochs,
        patience=args.patience,
        min_delta=args.min_delta,
        focal_loss_gamma=args.focal_loss_gamma
    )
    
    # 在测试集上评估模型
    print("\n在测试集上评估分层症状识别模型...")
    test_metrics = evaluate_hierarchical_model(
        model=model,
        test_loader=test_loader,
        symptom_types=symptom_types,
        device=device,
        optimize_thresholds=True
    )
    
    # 保存结果
    save_results(
        model=model,
        history=training_history,
        metrics=test_metrics,
        model_path=os.path.join(args.output_dir, "hierarchical_model.pth"),
        history_path=os.path.join(args.output_dir, "hierarchical_training_history.json"),
        metrics_path=os.path.join(args.output_dir, "hierarchical_test_results.json")
    )

if __name__ == "__main__":
    main()

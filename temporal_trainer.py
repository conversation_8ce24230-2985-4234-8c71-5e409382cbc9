import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_curve, auc, precision_recall_curve
import json
import os
from temporal_enhanced_model import HierarchicalLoss

class FocalLoss(nn.Module):
    """Focal Loss实现"""
    def __init__(self, gamma=2.0, weight=None, reduction='mean'):
        super(Focal<PERSON>oss, self).__init__()
        self.gamma = gamma
        self.weight = weight
        self.reduction = reduction
        
    def forward(self, input, target):
        ce_loss = nn.functional.cross_entropy(
            input, target, weight=self.weight, reduction='none'
        )
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

def calculate_class_weights(train_loader, symptom_types, device):
    """计算类别权重"""
    # 计算训练集中每个症状的类别分布
    symptom_class_counts = {symptom: [0, 0] for symptom in symptom_types}  # [负例数, 正例数]
    
    # 收集所有样本的标签
    for _, _, _, labels in train_loader:
        for i, symptom in enumerate(symptom_types):
            labels_np = labels[i].numpy()
            for label in labels_np:
                symptom_class_counts[symptom][label] += 1
    
    # 计算每个症状的类别权重
    class_weights = {}
    for symptom, counts in symptom_class_counts.items():
        # 避免除零错误
        if counts[0] == 0 or counts[1] == 0:
            class_weights[symptom] = torch.tensor([1.0, 1.0], device=device)
        else:
            # 计算反比例权重
            weight_0 = 1.0 / counts[0]
            weight_1 = 1.0 / counts[1]
            # 归一化权重
            total = weight_0 + weight_1
            weight_0 /= total
            weight_1 /= total
            # 增强少数类的权重
            if counts[1] < counts[0]:  # 如果正例是少数类
                # 根据不平衡程度动态调整权重
                imbalance_ratio = counts[0] / counts[1]
                # 限制最大权重增强倍数为5
                boost_factor = min(imbalance_ratio, 5.0)
                weight_1 *= boost_factor  # 增强正例权重
            else:
                weight_0 *= 2.5  # 增强负例权重
            
            class_weights[symptom] = torch.tensor([weight_0, weight_1], device=device)
    
    print("计算的类别权重:")
    for symptom, weights in class_weights.items():
        print(f"  {symptom}: {weights.cpu().numpy()}")
    
    return class_weights

def train_temporal_model(model, train_loader, val_loader, symptom_types, device, 
                        learning_rate=0.0005, weight_decay=1e-5, epochs=100,
                        patience=15, min_delta=0.001,
                        class_weight=None, focal_loss_gamma=2.0, 
                        use_focal_loss=True, use_hierarchical_loss=True,
                        use_scheduler=True):
    """
    训练时序增强的多症状模型
    
    参数:
    - model: 模型实例
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - symptom_types: 症状类型列表
    - device: 训练设备
    - learning_rate: 初始学习率
    - weight_decay: 权重衰减系数
    - epochs: 最大训练轮数
    - patience: 早停耐心值
    - min_delta: 早停最小改进阈值
    - class_weight: 类别权重，用于处理类别不平衡
    - focal_loss_gamma: Focal Loss的gamma参数
    - use_focal_loss: 是否使用Focal Loss
    - use_hierarchical_loss: 是否使用分层损失
    - use_scheduler: 是否使用学习率调度器
    
    返回:
    - 训练后的模型
    - 训练历史记录
    """
    # 使用AdamW优化器，提供更好的权重衰减
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    
    # 学习率调度器
    if use_scheduler:
        # 使用余弦退火学习率调度，带热重启
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=10, T_mult=2, eta_min=learning_rate/100
        )
    
    # 为每个症状类型计算类别权重
    if class_weight is None:
        class_weights = calculate_class_weights(train_loader, symptom_types, device)
    else:
        class_weights = class_weight
    
    # 用于记录训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rates': []
    }
    
    # 为每种症状添加指标记录
    for symptom in symptom_types:
        history[f'{symptom}_val_acc'] = []
        history[f'{symptom}_val_precision'] = []
        history[f'{symptom}_val_recall'] = []
        history[f'{symptom}_val_f1'] = []
        history[f'{symptom}_val_auc'] = []
        history[f'{symptom}_val_threshold'] = []
    
    # 早停设置
    best_val_loss = float('inf')
    best_model_state = None
    best_epoch = 0
    no_improve_count = 0
    
    # 创建分层损失函数
    hierarchical_losses = {
        symptom: HierarchicalLoss(presence_weight=0.3, type_weight=0.7)
        for symptom in symptom_types
    }
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for visual, audio, text, labels in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} - Training"):
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)
            labels = [label.to(device) for label in labels]  # 每个症状的标签
            
            # 前向传播
            outputs = model(visual, audio, text, training=True)
            
            # 计算每个症状的损失并求和
            loss = 0
            for i, (output, label) in enumerate(zip(outputs, labels)):
                symptom = symptom_types[i]
                
                if use_hierarchical_loss and isinstance(output, tuple):
                    # 使用分层损失
                    loss += hierarchical_losses[symptom](output, label)
                else:
                    # 使用常规损失
                    if use_focal_loss:
                        criterion = FocalLoss(gamma=focal_loss_gamma, weight=class_weights[symptom])
                        loss += criterion(output, label)
                    else:
                        criterion = nn.CrossEntropyLoss(weight=class_weights[symptom])
                        loss += criterion(output, label)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        history['learning_rates'].append(optimizer.param_groups[0]['lr'])
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        all_preds = {symptom: [] for symptom in symptom_types}
        all_labels = {symptom: [] for symptom in symptom_types}
        all_probs = {symptom: [] for symptom in symptom_types}  # 存储预测概率
        
        with torch.no_grad():
            for visual, audio, text, labels in tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} - Validation"):
                visual, audio, text = visual.to(device), audio.to(device), text.to(device)
                target_labels = [label.to(device) for label in labels]
                
                # 前向传播 - 推理模式
                outputs = model(visual, audio, text, training=False)
                
                # 计算损失
                batch_loss = 0
                for i, (output, label) in enumerate(zip(outputs, target_labels)):
                    symptom = symptom_types[i]
                    
                    # 在推理模式下，输出是预测概率
                    if isinstance(output, torch.Tensor) and output.size(1) == 2:
                        # 如果输出是logits，计算损失
                        if use_focal_loss:
                            criterion = FocalLoss(gamma=focal_loss_gamma, weight=class_weights[symptom])
                            batch_loss += criterion(output, label)
                        else:
                            criterion = nn.CrossEntropyLoss(weight=class_weights[symptom])
                            batch_loss += criterion(output, label)
                        
                        # 获取预测概率
                        probs = torch.softmax(output, dim=1)[:, 1].cpu().numpy()
                    else:
                        # 如果输出已经是概率，直接使用
                        probs = output.cpu().numpy()
                        # 使用二元交叉熵损失
                        criterion = nn.BCELoss()
                        batch_loss += criterion(output, label.float().unsqueeze(1))
                    
                    # 使用0.5作为初始阈值
                    preds = (probs > 0.5).astype(int)
                    true_labels = target_labels[i].cpu().numpy()
                    
                    all_probs[symptom].extend(probs)
                    all_preds[symptom].extend(preds)
                    all_labels[symptom].extend(true_labels)
                
                val_loss += batch_loss.item()
        
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # 检查是否是最佳模型
        if val_loss < best_val_loss - min_delta:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            best_epoch = epoch
            no_improve_count = 0
            print(f"发现新的最佳模型，验证损失: {val_loss:.4f}")
        else:
            no_improve_count += 1
            print(f"验证损失未改善，已经 {no_improve_count} 轮未改善")
        
        # 更新学习率
        if use_scheduler:
            if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step(val_loss)
            else:
                scheduler.step()
        
        # 计算每个症状的指标
        print(f"Epoch {epoch+1}/{epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        for i, symptom in enumerate(symptom_types):
            # 优化每个症状的阈值
            precision, recall, thresholds = precision_recall_curve(
                all_labels[symptom], all_probs[symptom]
            )
            
            # 计算每个阈值的F1分数
            f1_scores = 2 * precision * recall / (precision + recall + 1e-10)
            
            # 找到最佳F1分数对应的阈值
            best_idx = np.argmax(f1_scores)
            best_threshold = thresholds[best_idx] if best_idx < len(thresholds) else 0.5
            
            # 使用最佳阈值重新计算预测
            optimized_preds = (np.array(all_probs[symptom]) > best_threshold).astype(int)
            
            # 计算指标
            acc = accuracy_score(all_labels[symptom], optimized_preds)
            prec = precision_score(all_labels[symptom], optimized_preds, zero_division=0)
            rec = recall_score(all_labels[symptom], optimized_preds, zero_division=0)
            f1 = f1_score(all_labels[symptom], optimized_preds, zero_division=0)
            
            # 计算AUC
            fpr, tpr, _ = roc_curve(all_labels[symptom], all_probs[symptom])
            roc_auc = auc(fpr, tpr)
            
            history[f'{symptom}_val_acc'].append(acc)
            history[f'{symptom}_val_precision'].append(prec)
            history[f'{symptom}_val_recall'].append(rec)
            history[f'{symptom}_val_f1'].append(f1)
            history[f'{symptom}_val_auc'].append(roc_auc)
            history[f'{symptom}_val_threshold'].append(best_threshold)
            
            print(f"  {symptom} - Acc: {acc:.4f}, Precision: {prec:.4f}, Recall: {rec:.4f}, F1: {f1:.4f}, AUC: {roc_auc:.4f}, Threshold: {best_threshold:.4f}")
        
        # 早停检查
        if no_improve_count >= patience:
            print(f"早停触发！已经 {patience} 轮验证损失未改善")
            break
    
    # 恢复最佳模型
    print(f"使用第 {best_epoch+1} 轮的最佳模型，验证损失: {best_val_loss:.4f}")
    model.load_state_dict(best_model_state)
    
    return model, history

def evaluate_temporal_model(model, test_loader, symptom_types, device, 
                           custom_thresholds=None, optimize_thresholds=True):
    """
    评估时序增强模型性能
    
    参数:
    - model: 模型实例
    - test_loader: 测试数据加载器
    - symptom_types: 症状类型列表
    - device: 训练设备
    - custom_thresholds: 自定义决策阈值字典 {symptom: threshold}
    - optimize_thresholds: 是否优化决策阈值
    
    返回:
    - 每个症状的评估指标
    """
    model.eval()
    all_preds = {symptom: [] for symptom in symptom_types}
    all_labels = {symptom: [] for symptom in symptom_types}
    all_probs = {symptom: [] for symptom in symptom_types}  # 存储预测概率
    
    with torch.no_grad():
        for visual, audio, text, labels in tqdm(test_loader, desc="Testing"):
            visual, audio, text = visual.to(device), audio.to(device), text.to(device)
            target_labels = [label.to(device) for label in labels]
            
            # 前向传播 - 推理模式
            outputs = model(visual, audio, text, training=False)
            
            # 收集预测和标签，用于计算指标
            for i, symptom in enumerate(symptom_types):
                output = outputs[i]
                
                # 获取预测概率
                if isinstance(output, torch.Tensor) and output.size(1) == 2:
                    # 如果输出是logits，获取概率
                    probs = torch.softmax(output, dim=1)[:, 1].cpu().numpy()
                else:
                    # 如果输出已经是概率，直接使用
                    probs = output.cpu().numpy()
                
                true_labels = target_labels[i].cpu().numpy()
                
                all_probs[symptom].extend(probs)
                all_labels[symptom].extend(true_labels)
    
    # 如果需要优化阈值且没有提供自定义阈值
    if optimize_thresholds and custom_thresholds is None:
        # 优化每个症状的阈值
        custom_thresholds = {}
        
        for symptom in symptom_types:
            # 优化每个症状的阈值
            precision, recall, thresholds = precision_recall_curve(
                all_labels[symptom], all_probs[symptom]
            )
            
            # 计算每个阈值的F1分数
            f1_scores = 2 * precision * recall / (precision + recall + 1e-10)
            
            # 找到最佳F1分数对应的阈值
            best_idx = np.argmax(f1_scores)
            best_threshold = thresholds[best_idx] if best_idx < len(thresholds) else 0.5
            
            custom_thresholds[symptom] = best_threshold
            print(f"为 {symptom} 优化的阈值: {best_threshold:.4f}")
    
    # 使用阈值进行预测
    for symptom in symptom_types:
        threshold = custom_thresholds.get(symptom, 0.5) if custom_thresholds else 0.5
        all_preds[symptom] = (np.array(all_probs[symptom]) > threshold).astype(int)
    
    # 计算每个症状的指标
    metrics = {}
    
    for i, symptom in enumerate(symptom_types):
        true_labels = all_labels[symptom]
        preds = all_preds[symptom]
        probs = all_probs[symptom]
        
        # 计算基本指标
        accuracy = accuracy_score(true_labels, preds)
        precision = precision_score(true_labels, preds, zero_division=0)
        recall = recall_score(true_labels, preds, zero_division=0)
        f1 = f1_score(true_labels, preds, zero_division=0)
        
        # 计算ROC曲线和AUC
        fpr, tpr, _ = roc_curve(true_labels, probs)
        roc_auc = auc(fpr, tpr)
        
        # 计算混淆矩阵元素
        tp = np.sum((np.array(preds) == 1) & (np.array(true_labels) == 1))
        fp = np.sum((np.array(preds) == 1) & (np.array(true_labels) == 0))
        tn = np.sum((np.array(preds) == 0) & (np.array(true_labels) == 0))
        fn = np.sum((np.array(preds) == 0) & (np.array(true_labels) == 1))
        
        metrics[symptom] = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': roc_auc,
            'threshold': custom_thresholds.get(symptom, 0.5) if custom_thresholds else 0.5,
            'tp': int(tp),
            'fp': int(fp),
            'tn': int(tn),
            'fn': int(fn)
        }
        
        print(f"{symptom} 测试结果:")
        print(f"  准确率: {accuracy:.4f}")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  F1分数: {f1:.4f}")
        print(f"  AUC: {roc_auc:.4f}")
        print(f"  阈值: {metrics[symptom]['threshold']:.4f}")
        print(f"  混淆矩阵: TP={tp}, FP={fp}, TN={tn}, FN={fn}")
    
    # 计算平均指标
    avg_accuracy = sum(metrics[symptom]['accuracy'] for symptom in symptom_types) / len(symptom_types)
    avg_precision = sum(metrics[symptom]['precision'] for symptom in symptom_types) / len(symptom_types)
    avg_recall = sum(metrics[symptom]['recall'] for symptom in symptom_types) / len(symptom_types)
    avg_f1 = sum(metrics[symptom]['f1'] for symptom in symptom_types) / len(symptom_types)
    avg_auc = sum(metrics[symptom]['auc'] for symptom in symptom_types) / len(symptom_types)
    
    print(f"\n平均指标:")
    print(f"  平均准确率: {avg_accuracy:.4f}")
    print(f"  平均精确率: {avg_precision:.4f}")
    print(f"  平均召回率: {avg_recall:.4f}")
    print(f"  平均F1分数: {avg_f1:.4f}")
    print(f"  平均AUC: {avg_auc:.4f}")
    
    # 添加平均指标到结果中
    metrics['average'] = {
        'accuracy': avg_accuracy,
        'precision': avg_precision,
        'recall': avg_recall,
        'f1': avg_f1,
        'auc': avg_auc
    }
    
    return metrics

def save_results(model, history, metrics, model_path, history_path, metrics_path):
    """保存模型、训练历史和评估指标"""
    # 保存模型
    torch.save(model.state_dict(), model_path)
    print(f"模型已保存到 {model_path}")
    
    # 保存训练历史
    with open(history_path, "w", encoding="utf-8") as f:
        # 将tensor转换为可序列化的格式
        serializable_history = {}
        for key, value in history.items():
            if isinstance(value, list):
                # 如果值是列表，检查列表中的每个元素
                serializable_history[key] = [float(item) if isinstance(item, (torch.Tensor, np.ndarray, np.float32, np.float64)) else item for item in value]
            else:
                serializable_history[key] = float(value) if isinstance(value, (torch.Tensor, np.ndarray, np.float32, np.float64)) else value
        
        json.dump(serializable_history, f, ensure_ascii=False, indent=4)
    
    print(f"训练历史已保存到 {history_path}")
    
    # 保存评估指标
    with open(metrics_path, "w", encoding="utf-8") as f:
        # 将NumPy数据类型转换为Python原生数据类型
        serializable_metrics = {}
        for symptom, symptom_metrics in metrics.items():
            serializable_metrics[symptom] = {}
            for metric_name, metric_value in symptom_metrics.items():
                if isinstance(metric_value, (np.number, np.ndarray, np.float32, np.float64)):
                    serializable_metrics[symptom][metric_name] = float(metric_value)
                else:
                    serializable_metrics[symptom][metric_name] = metric_value
        
        json.dump(serializable_metrics, f, ensure_ascii=False, indent=4)
    
    print(f"评估指标已保存到 {metrics_path}")

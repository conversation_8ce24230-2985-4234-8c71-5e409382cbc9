#!/bin/bash

# 设置数据目录和标签文件
DATA_DIR="./shuju"
LABEL_FILE="./symptom_label_template.xlsx"

# 创建输出目录
mkdir -p ./temporal_results
mkdir -p ./hierarchical_results
mkdir -p ./comparison_results

# 运行时序增强模型
echo "运行时序增强模型..."
python run_temporal_model.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 100 \
    --learning_rate 0.0005 \
    --patience 15 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./temporal_results

# 运行分层分类模型
echo "运行分层分类模型..."
python run_hierarchical_model.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 100 \
    --learning_rate 0.0005 \
    --patience 15 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./hierarchical_results

# 比较模型性能
echo "比较模型性能..."
python compare_models.py \
    --baseline_metrics ./cross_modal_consistency_test_results.json \
    --temporal_metrics ./temporal_results/temporal_test_results.json \
    --hierarchical_metrics ./hierarchical_results/hierarchical_test_results.json \
    --output_dir ./comparison_results

echo "所有模型运行完成！"

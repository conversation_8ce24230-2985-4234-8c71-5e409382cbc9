#!/bin/bash

# 运行结合时序增强和跨模态一致性增强的模型

# 创建输出目录
mkdir -p ./combined_results

# 运行模型
python3 run_combined_model.py \
    --data_dir ./shuju \
    --label_file ./symptom_label_template.xlsx \
    --output_dir ./combined_results \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --num_layers 2 \
    --dropout 0.3 \
    --batch_size 32 \
    --epochs 100 \
    --learning_rate 0.0005 \
    --weight_decay 0.0001 \
    --patience 10 \
    --augment_prob 0.2 \
    --seed 42

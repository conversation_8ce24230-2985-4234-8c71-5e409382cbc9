import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import pickle
import csv
from sklearn.model_selection import train_test_split

class TemporalFeatureExtractor:
    """时序特征提取器"""
    def __init__(self, data_dir):
        self.data_dir = data_dir

    def extract_temporal_features(self, sample_id, question_num):
        """
        提取时序特征

        参数:
        - sample_id: 样本ID
        - question_num: 问题编号

        返回:
        - 视觉时序特征
        - 音频时序特征
        - 文本时序特征
        """
        features = {}

        # 基础路径
        sample_dir = os.path.join(self.data_dir, f"{sample_id}_Q{question_num}")

        # 1. 提取视觉时序特征
        openface_path = os.path.join(sample_dir, f"Q{question_num}.csv")
        if os.path.exists(openface_path):
            try:
                df = pd.read_csv(openface_path)

                # 提取面部动作单元(AU)的动态变化
                au_intensity_columns = [col for col in df.columns if 'AU' in col and '_r' in col]  # AU强度
                pose_columns = [col for col in df.columns if 'pose' in col]
                gaze_columns = [col for col in df.columns if 'gaze' in col]

                # 组合所有特征
                visual_columns = au_intensity_columns + pose_columns + gaze_columns

                if visual_columns:
                    # 获取时序数据
                    visual_temporal = df[visual_columns].values

                    # 如果序列太长，进行下采样
                    max_seq_len = 100
                    if visual_temporal.shape[0] > max_seq_len:
                        indices = np.linspace(0, visual_temporal.shape[0]-1, max_seq_len, dtype=int)
                        visual_temporal = visual_temporal[indices]

                    features['visual_temporal'] = visual_temporal
            except Exception as e:
                print(f"处理OpenFace特征出错 {sample_dir}: {e}")

        # 2. 提取音频时序特征
        audio_path = os.path.join(sample_dir, f"Q{question_num}.wav.csv")
        if os.path.exists(audio_path):
            try:
                # 读取CSV文件
                audio_data = []
                with open(audio_path, 'r') as f:
                    for line in f:
                        try:
                            values = [float(x) for x in line.strip().split(',') if x.strip()]
                            audio_data.append(values)
                        except:
                            continue

                if audio_data:
                    audio_temporal = np.array(audio_data)

                    # 如果序列太长，进行下采样
                    max_seq_len = 100
                    if audio_temporal.shape[0] > max_seq_len:
                        indices = np.linspace(0, audio_temporal.shape[0]-1, max_seq_len, dtype=int)
                        audio_temporal = audio_temporal[indices]

                    features['audio_temporal'] = audio_temporal
            except Exception as e:
                print(f"处理音频特征出错 {sample_dir}: {e}")

        # 3. 提取文本特征 (BERT嵌入)
        # 注意：文本通常没有时序性，但我们可以将其视为单个时间步
        bert_path = os.path.join(sample_dir, f"vector_Q{question_num}.csv")
        if os.path.exists(bert_path):
            try:
                # 检查文件是否为空
                if os.path.getsize(bert_path) > 0:
                    with open(bert_path, 'r') as f:
                        csv_reader = csv.reader(f)
                        bert_embedding = next(csv_reader)  # 读取第一行
                        if bert_embedding and len(bert_embedding) > 0:
                            # 将字符串转换为浮点数
                            bert_embedding = [float(val) for val in bert_embedding]
                            features['text_embedding'] = np.array(bert_embedding)
            except Exception as e:
                print(f"处理BERT嵌入特征出错 {sample_dir}: {e}")

        return features

class TemporalDataset(Dataset):
    """时序数据集"""
    def __init__(self, data_dir, samples, symptom_types, max_seq_len=100, augment_prob=0.0):
        """
        初始化数据集

        参数:
        - data_dir: 数据目录
        - samples: 样本列表 [(sample_id, question_num, labels), ...]
        - symptom_types: 症状类型列表
        - max_seq_len: 最大序列长度
        - augment_prob: 数据增强概率
        """
        self.data_dir = data_dir
        self.samples = samples
        self.symptom_types = symptom_types
        self.max_seq_len = max_seq_len
        self.augment_prob = augment_prob

        # 特征提取器
        self.feature_extractor = TemporalFeatureExtractor(data_dir)

        # 缓存
        self.cache = {}

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        # 检查缓存
        if idx in self.cache:
            return self.cache[idx]

        sample_id, question_num, labels = self.samples[idx]

        # 提取时序特征
        features = self.feature_extractor.extract_temporal_features(sample_id, question_num)

        # 处理视觉时序特征
        if 'visual_temporal' in features:
            visual_temporal = features['visual_temporal']

            # 确保序列长度一致
            if visual_temporal.shape[0] < self.max_seq_len:
                # 填充序列
                padding = np.zeros((self.max_seq_len - visual_temporal.shape[0], visual_temporal.shape[1]))
                visual_temporal = np.vstack([visual_temporal, padding])
            elif visual_temporal.shape[0] > self.max_seq_len:
                # 截断序列
                visual_temporal = visual_temporal[:self.max_seq_len]

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 128
            if visual_temporal.shape[1] > fixed_dim:
                # 如果特征维度太大，截断
                visual_temporal = visual_temporal[:, :fixed_dim]
            elif visual_temporal.shape[1] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros((visual_temporal.shape[0], fixed_dim - visual_temporal.shape[1]))
                visual_temporal = np.hstack([visual_temporal, padding])
        else:
            # 如果没有视觉特征，使用零向量
            visual_temporal = np.zeros((self.max_seq_len, 128))  # 固定视觉特征维度为128

        # 处理音频时序特征
        if 'audio_temporal' in features:
            audio_temporal = features['audio_temporal']

            # 确保序列长度一致
            if audio_temporal.shape[0] < self.max_seq_len:
                # 填充序列
                padding = np.zeros((self.max_seq_len - audio_temporal.shape[0], audio_temporal.shape[1]))
                audio_temporal = np.vstack([audio_temporal, padding])
            elif audio_temporal.shape[0] > self.max_seq_len:
                # 截断序列
                audio_temporal = audio_temporal[:self.max_seq_len]

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 128
            if audio_temporal.shape[1] > fixed_dim:
                # 如果特征维度太大，截断
                audio_temporal = audio_temporal[:, :fixed_dim]
            elif audio_temporal.shape[1] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros((audio_temporal.shape[0], fixed_dim - audio_temporal.shape[1]))
                audio_temporal = np.hstack([audio_temporal, padding])
        else:
            # 如果没有音频特征，使用零向量
            audio_temporal = np.zeros((self.max_seq_len, 128))  # 固定音频特征维度为128

        # 处理文本特征
        if 'text_embedding' in features:
            text_embedding = features['text_embedding']

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 768  # BERT嵌入维度
            if text_embedding.shape[0] > fixed_dim:
                # 如果特征维度太大，截断
                text_embedding = text_embedding[:fixed_dim]
            elif text_embedding.shape[0] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros(fixed_dim - text_embedding.shape[0])
                text_embedding = np.concatenate([text_embedding, padding])

            # 文本特征通常是静态的，我们可以将其复制为序列
            text_temporal = np.tile(text_embedding, (self.max_seq_len, 1))
        else:
            # 如果没有文本特征，使用零向量
            text_temporal = np.zeros((self.max_seq_len, 768))  # BERT嵌入维度为768

        # 应用数据增强
        if np.random.random() < self.augment_prob:
            visual_temporal = self._augment_temporal_feature(visual_temporal)
            audio_temporal = self._augment_temporal_feature(audio_temporal)
            # 文本特征不进行时序增强

        # 转换为张量
        visual_tensor = torch.tensor(visual_temporal, dtype=torch.float32)
        audio_tensor = torch.tensor(audio_temporal, dtype=torch.float32)
        text_tensor = torch.tensor(text_temporal, dtype=torch.float32)

        # 转换标签为张量
        label_tensors = [torch.tensor(label, dtype=torch.long) for label in labels]

        # 缓存结果
        result = (visual_tensor, audio_tensor, text_tensor, label_tensors)
        self.cache[idx] = result

        return result

    def _augment_temporal_feature(self, feature):
        """时序特征增强"""
        # 复制原始特征
        augmented_feature = feature.copy()

        # 添加小幅度噪声
        noise_scale = 0.02  # 2%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise

        # 随机时间掩码 - 掩盖一些时间步
        mask_ratio = 0.05  # 5%的时间步被掩码
        time_steps = augmented_feature.shape[0]
        mask_indices = np.random.choice(time_steps, int(time_steps * mask_ratio), replace=False)
        augmented_feature[mask_indices] = 0

        # 随机特征掩码 - 掩盖一些特征维度
        mask_ratio = 0.05  # 5%的特征被掩码
        feature_dims = augmented_feature.shape[1]
        mask_indices = np.random.choice(feature_dims, int(feature_dims * mask_ratio), replace=False)
        augmented_feature[:, mask_indices] = 0

        return augmented_feature

def create_temporal_dataloader(data_dir, samples, symptom_types,
                              max_seq_len=100, augment_prob=0.0,
                              batch_size=32, shuffle=True):
    """创建时序数据加载器"""
    dataset = TemporalDataset(
        data_dir=data_dir,
        samples=samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        augment_prob=augment_prob
    )

    return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)

def prepare_temporal_data(data_dir, label_file, symptom_types,
                         max_seq_len=100, batch_size=32,
                         test_size=0.15, val_size=0.15, seed=42,
                         augment_prob=0.2):
    """准备时序训练、验证和测试数据加载器"""
    # 设置随机种子
    np.random.seed(seed)
    torch.manual_seed(seed)

    # 读取标签文件
    df = pd.read_excel(label_file)

    # 获取所有有效的样本目录
    all_sample_dirs = set()
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            all_sample_dirs.add(item)

    # 收集有效样本
    valid_samples = []

    for sample_full_id in df['SampleID'].tolist():
        # 检查目录是否存在
        if sample_full_id in all_sample_dirs:
            sample_dir = os.path.join(data_dir, sample_full_id)

            # 解析样本ID和问题编号
            sample_id, q_part = sample_full_id.split('_')
            question_num = int(q_part[1:])

            # 提取多个症状标签
            try:
                row = df[df['SampleID'] == sample_full_id].iloc[0]
                labels = [int(row[symptom]) for symptom in symptom_types]
                valid_samples.append((sample_id, question_num, labels))
                print(f"添加样本: {sample_full_id}")
            except Exception as e:
                print(f"提取标签出错 {sample_full_id}: {e}")

    print(f"有效样本数：{len(valid_samples)}")

    if len(valid_samples) == 0:
        raise ValueError("没有找到有效样本，请检查数据目录和标签文件")

    # 划分训练、验证和测试集
    train_val_samples, test_samples = train_test_split(valid_samples, test_size=test_size, random_state=seed)
    train_samples, val_samples = train_test_split(train_val_samples, test_size=val_size/(1-test_size), random_state=seed)

    print(f"训练集：{len(train_samples)}，验证集：{len(val_samples)}，测试集：{len(test_samples)}")

    # 创建数据加载器
    train_loader = create_temporal_dataloader(
        data_dir=data_dir,
        samples=train_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        augment_prob=augment_prob,
        batch_size=batch_size,
        shuffle=True
    )

    # 验证和测试集不使用增强
    val_loader = create_temporal_dataloader(
        data_dir=data_dir,
        samples=val_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        batch_size=batch_size,
        shuffle=False
    )

    test_loader = create_temporal_dataloader(
        data_dir=data_dir,
        samples=test_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        batch_size=batch_size,
        shuffle=False
    )

    return train_loader, val_loader, test_loader, symptom_types

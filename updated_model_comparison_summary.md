# 更新后的模型比较总结

## 总体性能指标

| 模型 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
|-------|----------|-----------|--------|----------|-----|
| GRU-BiLSTM | 0.8642 | 0.6780 | 0.7708 | 0.7125 | 0.9046 |
| Cross-Attention | 0.8333 | 0.6046 | 0.8438 | 0.6891 | 0.8930 |
| Self-Attention | 0.8302 | 0.5694 | 0.8458 | 0.6782 | 0.8964 |
| CubeMLP | 0.7654 | 0.5278 | 0.8354 | 0.6151 | 0.9000 |
| BiLSTM | 0.7778 | 0.5602 | 0.7688 | 0.5991 | 0.8779 |
| PMR | 0.7222 | 0.5010 | 0.7604 | 0.5616 | 0.8546 |
| MulT (修复后) | 0.4167 | 0.2588 | 0.9000 | 0.4019 | 0.6266 |

## 各症状类型的性能

### 情绪症状

| 模型 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
|-------|----------|-----------|--------|----------|-----|
| GRU-BiLSTM | 0.8889 | 0.8261 | 0.7917 | 0.8085 | 0.8969 |
| Cross-Attention | 0.8395 | 0.6897 | 0.8333 | 0.7547 | 0.8955 |
| Self-Attention | 0.8395 | 0.6774 | 0.8750 | 0.7636 | 0.9123 |
| CubeMLP | 0.8272 | 0.6786 | 0.7917 | 0.7308 | 0.9240 |
| BiLSTM | 0.8765 | 0.8889 | 0.6667 | 0.7619 | 0.9232 |
| PMR | 0.8642 | 0.8824 | 0.6250 | 0.7317 | 0.8860 |
| MulT (修复后) | 0.2963 | 0.2963 | 1.0000 | 0.4571 | 0.5062 |

### 躯体症状

| 模型 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
|-------|----------|-----------|--------|----------|-----|
| GRU-BiLSTM | 0.8765 | 0.6923 | 0.6000 | 0.6429 | 0.8667 |
| Cross-Attention | 0.8889 | 0.7143 | 0.6667 | 0.6897 | 0.8727 |
| Self-Attention | 0.8148 | 0.5000 | 0.7333 | 0.5946 | 0.8566 |
| CubeMLP | 0.8642 | 0.6000 | 0.8000 | 0.6857 | 0.8899 |
| BiLSTM | 0.8395 | 0.5714 | 0.5333 | 0.5517 | 0.8212 |
| PMR | 0.5432 | 0.2609 | 0.8000 | 0.3934 | 0.8071 |
| MulT (修复后) | 0.4568 | 0.2264 | 0.8000 | 0.3529 | 0.6828 |

### 认知症状

| 模型 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
|-------|----------|-----------|--------|----------|-----|
| GRU-BiLSTM | 0.7778 | 0.5938 | 0.7917 | 0.6786 | 0.8845 |
| Cross-Attention | 0.7407 | 0.5385 | 0.8750 | 0.6667 | 0.8516 |
| Self-Attention | 0.7901 | 0.6000 | 0.8750 | 0.7119 | 0.8604 |
| CubeMLP | 0.7778 | 0.6000 | 0.7500 | 0.6667 | 0.8480 |
| BiLSTM | 0.6790 | 0.4773 | 0.8750 | 0.6176 | 0.8319 |
| PMR | 0.6420 | 0.4490 | 0.9167 | 0.6027 | 0.8268 |
| MulT (修复后) | 0.2963 | 0.2963 | 1.0000 | 0.4571 | 0.5936 |

### 行为症状

| 模型 | 准确率 | 精确率 | 召回率 | F1分数 | AUC |
|-------|----------|-----------|--------|----------|-----|
| GRU-BiLSTM | 0.9136 | 0.6000 | 0.9000 | 0.7200 | 0.9704 |
| Cross-Attention | 0.8642 | 0.4762 | 1.0000 | 0.6452 | 0.9521 |
| Self-Attention | 0.8765 | 0.5000 | 0.9000 | 0.6429 | 0.9563 |
| CubeMLP | 0.5926 | 0.2326 | 1.0000 | 0.3774 | 0.9380 |
| BiLSTM | 0.7160 | 0.3030 | 1.0000 | 0.4651 | 0.9352 |
| PMR | 0.8395 | 0.4118 | 0.7000 | 0.5185 | 0.8986 |
| MulT (修复后) | 0.6173 | 0.2162 | 0.8000 | 0.3404 | 0.7239 |

## 主要发现

1. **最佳整体模型**：GRU-BiLSTM仍然是性能最好的模型，具有最高的准确率(0.8642)、F1分数(0.7125)和AUC(0.9046)。

2. **注意力机制**：Self-Attention和Cross-Attention模型表现良好，Cross-Attention在总体准确率和F1分数方面略优于Self-Attention。

3. **CubeMLP性能**：简化后的CubeMLP模型显示出有竞争力的性能，特别是在AUC(0.9000)方面，证明了基于MLP的方法的有效性。

4. **PMR模型**：渐进式多模态推理(PMR)模型表现中等，在情绪症状方面表现特别好。

5. **MulT模型改进**：修复后的MulT模型性能有所提升，但仍然是所有模型中表现最差的。召回率很高(0.9000)，但精确率很低(0.2588)，表明模型仍然倾向于过度预测正类。

6. **症状特定性能**：
   - 情绪症状：GRU-BiLSTM和BiLSTM模型表现最佳
   - 躯体症状：Cross-Attention、GRU-BiLSTM和CubeMLP模型表现最佳
   - 认知症状：Self-Attention和GRU-BiLSTM模型表现最佳
   - 行为症状：GRU-BiLSTM模型明显优于其他模型

7. **召回率与精确率权衡**：大多数模型显示出比精确率更高的召回率，表明它们倾向于以一些假阳性为代价识别更多的阳性病例。

## 结论

GRU-BiLSTM模型在多模态抑郁症状检测方面表现最佳，其次是基于注意力的模型。GRU-BiLSTM的时序建模能力对于这项任务特别有效。CubeMLP模型以更简单的架构展示了有希望的结果，表明基于MLP的方法可以成为更复杂模型的有竞争力的替代方案。

尽管经过修复，MulT模型仍然表现不佳，可能需要进一步的架构调整或训练策略优化。高召回率但低精确率表明模型仍然存在过度预测正类的问题。

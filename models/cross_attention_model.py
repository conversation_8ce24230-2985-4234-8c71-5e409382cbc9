import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class CrossModalAttention(nn.Module):
    """跨模态注意力机制"""
    def __init__(self, hidden_dim, num_heads=4, dropout=0.1):
        super(CrossModalAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        assert self.head_dim * num_heads == hidden_dim, "hidden_dim必须能被num_heads整除"
        
        # 投影层
        self.query_proj = nn.Linear(hidden_dim, hidden_dim)
        self.key_proj = nn.Linear(hidden_dim, hidden_dim)
        self.value_proj = nn.Linear(hidden_dim, hidden_dim)
        
        # 输出层
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
        # 缩放因子
        self.scale = math.sqrt(self.head_dim)
        
    def forward(self, query, key, value):
        """
        前向传播
        
        参数:
            query: 查询特征 [batch_size, seq_len_q, hidden_dim]
            key: 键特征 [batch_size, seq_len_k, hidden_dim]
            value: 值特征 [batch_size, seq_len_v, hidden_dim]
            
        返回:
            attention_output: 注意力输出 [batch_size, seq_len_q, hidden_dim]
            attention_weights: 注意力权重 [batch_size, num_heads, seq_len_q, seq_len_k]
        """
        batch_size = query.size(0)
        
        # 投影查询、键和值
        q = self.query_proj(query)
        k = self.key_proj(key)
        v = self.value_proj(value)
        
        # 重塑为多头形式
        q = q.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / self.scale
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        context = torch.matmul(attention_weights, v)
        
        # 重塑回原始形状
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.hidden_dim)
        
        # 输出投影
        attention_output = self.out_proj(context)
        attention_output = self.dropout(attention_output)
        
        # 残差连接和层归一化
        attention_output = self.layer_norm(query + attention_output)
        
        return attention_output, attention_weights

class CrossAttentionModel(nn.Module):
    """基于跨模态注意力的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, num_heads=4, dropout=0.3):
        super(CrossAttentionModel, self).__init__()
        
        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 跨模态注意力层
        self.v2a_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 视觉对音频
        self.v2t_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 视觉对文本
        self.a2v_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 音频对视觉
        self.a2t_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 音频对文本
        self.t2v_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 文本对视觉
        self.t2a_attention = CrossModalAttention(hidden_dim, num_heads, dropout)  # 文本对音频
        
        # 自适应权重层
        self.adaptive_weight = nn.Sequential(
            nn.Linear(hidden_dim * 9, hidden_dim * 3),
            nn.LayerNorm(hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 3, 3),
            nn.Softmax(dim=1)
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])
        
    def _prepare_sequence(self, x):
        """将静态特征转换为序列形式，以便进行注意力处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)
        
        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x
            
        # 否则，创建一个假的序列 (长度为1的序列)
        sequence = x.unsqueeze(1)
        
        return sequence
        
    def forward(self, visual, audio, text):
        """
        前向传播
        
        参数:
            visual: 视觉特征 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, audio_dim]
            text: 文本特征 [batch_size, text_dim]
            
        返回:
            outputs: 各症状的分类结果列表
        """
        batch_size = visual.size(0)
        
        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 2. 准备序列数据
        visual_seq = self._prepare_sequence(visual_encoded)
        audio_seq = self._prepare_sequence(audio_encoded)
        text_seq = self._prepare_sequence(text_encoded)
        
        # 3. 应用跨模态注意力
        v2a_out, _ = self.v2a_attention(visual_seq, audio_seq, audio_seq)
        v2t_out, _ = self.v2t_attention(visual_seq, text_seq, text_seq)
        a2v_out, _ = self.a2v_attention(audio_seq, visual_seq, visual_seq)
        a2t_out, _ = self.a2t_attention(audio_seq, text_seq, text_seq)
        t2v_out, _ = self.t2v_attention(text_seq, visual_seq, visual_seq)
        t2a_out, _ = self.t2a_attention(text_seq, audio_seq, audio_seq)
        
        # 4. 提取特征 (如果是序列，取平均)
        visual_feat = visual_seq.mean(dim=1)
        audio_feat = audio_seq.mean(dim=1)
        text_feat = text_seq.mean(dim=1)
        v2a_feat = v2a_out.mean(dim=1)
        v2t_feat = v2t_out.mean(dim=1)
        a2v_feat = a2v_out.mean(dim=1)
        a2t_feat = a2t_out.mean(dim=1)
        t2v_feat = t2v_out.mean(dim=1)
        t2a_feat = t2a_out.mean(dim=1)
        
        # 5. 计算自适应权重
        concat_feat = torch.cat([
            visual_feat, audio_feat, text_feat,
            v2a_feat, v2t_feat, a2v_feat, a2t_feat, t2v_feat, t2a_feat
        ], dim=1)
        
        modality_weights = self.adaptive_weight(concat_feat)
        
        # 6. 应用自适应权重
        weighted_visual = visual_feat * modality_weights[:, 0].unsqueeze(1)
        weighted_audio = audio_feat * modality_weights[:, 1].unsqueeze(1)
        weighted_text = text_feat * modality_weights[:, 2].unsqueeze(1)
        
        # 7. 特征融合
        weighted_concat = torch.cat([weighted_visual, weighted_audio, weighted_text], dim=1)
        fused_features = self.fusion(weighted_concat)
        
        # 8. 多症状分类
        outputs = [classifier(fused_features) for classifier in self.symptom_classifiers]
        
        return outputs

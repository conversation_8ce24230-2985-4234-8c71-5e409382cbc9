import torch
import torch.nn as nn
import torch.nn.functional as F

class CubeMLPLayer(nn.Module):
    """简化版立方体MLP层"""
    def __init__(self, hidden_dim, dropout=0.1):
        super(CubeMLPLayer, self).__init__()

        # 使用一个简单的MLP处理
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Dropout(dropout)
        )

        # 层归一化
        self.norm = nn.LayerNorm(hidden_dim)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入特征 [batch_size, seq_len, hidden_dim]

        返回:
            output: 处理后的特征 [batch_size, seq_len, hidden_dim]
        """
        # 残差连接
        residual = x

        # 应用MLP
        x = self.mlp(x)

        # 残差连接和归一化
        x = residual + x
        x = self.norm(x)

        return x

class CubeMLPModel(nn.Module):
    """基于立方体MLP的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, num_layers=2, dropout=0.3):
        super(CubeMLPModel, self).__init__()

        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 立方体MLP层
        self.cube_mlp_layers = nn.ModuleList([
            CubeMLPLayer(hidden_dim, dropout) for _ in range(num_layers)
        ])

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])

    def forward(self, visual, audio, text):
        """
        前向传播

        参数:
            visual: 视觉特征 [batch_size, seq_len, visual_dim] 或 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, seq_len, audio_dim] 或 [batch_size, audio_dim]
            text: 文本特征 [batch_size, seq_len, text_dim] 或 [batch_size, text_dim]

        返回:
            outputs: 各症状的分类结果列表
        """
        # 处理输入维度
        if len(visual.shape) == 3:
            # 如果是序列数据，取平均值
            visual = visual.mean(dim=1)
            audio = audio.mean(dim=1)
            text = text.mean(dim=1)

        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)

        # 2. 应用立方体MLP层
        for layer in self.cube_mlp_layers:
            visual_encoded = layer(visual_encoded)
            audio_encoded = layer(audio_encoded)
            text_encoded = layer(text_encoded)

        # 3. 特征融合
        concat_features = torch.cat([visual_encoded, audio_encoded, text_encoded], dim=1)
        fused_features = self.fusion(concat_features)

        # 4. 多症状分类
        outputs = [classifier(fused_features) for classifier in self.symptom_classifiers]

        return outputs

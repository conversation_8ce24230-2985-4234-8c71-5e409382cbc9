import torch
import torch.nn as nn
import torch.nn.functional as F

class BiLSTMEncoder(nn.Module):
    """双向LSTM编码器，用于时序特征提取"""
    def __init__(self, input_dim, hidden_dim, num_layers=2, dropout=0.2):
        super(BiLSTMEncoder, self).__init__()
        
        # BiLSTM层
        self.bilstm = nn.LSTM(
            input_dim,
            hidden_dim // 2,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 层归一化
        self.norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入序列 [batch_size, seq_len, input_dim]
            
        返回:
            output: 最终隐藏状态 [batch_size, hidden_dim]
            seq_output: 序列输出 [batch_size, seq_len, hidden_dim]
        """
        # BiLSTM处理
        lstm_out, (hidden, _) = self.bilstm(x)
        
        # 连接前向和后向的最后隐藏状态
        last_hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)
        
        # 应用层归一化
        last_hidden = self.norm(last_hidden)
        
        return last_hidden, lstm_out

class BiLSTMModel(nn.Module):
    """基于双向LSTM的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, num_layers=2, dropout=0.3):
        super(BiLSTMModel, self).__init__()
        
        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # BiLSTM编码器
        self.visual_bilstm = BiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)
        self.audio_bilstm = BiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)
        self.text_bilstm = BiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)
        
        # 自适应权重层
        self.adaptive_weight = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 3),
            nn.Softmax(dim=1)
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])
        
    def _prepare_sequence(self, x, seq_len=10):
        """将静态特征转换为序列形式，以便进行时序处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)
        
        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x
            
        # 否则，创建一个假的序列
        # 通过添加少量噪声创建序列变化
        noise_scale = 0.05
        sequence = x.unsqueeze(1).expand(batch_size, seq_len, feature_dim)
        noise = torch.randn(batch_size, seq_len, feature_dim, device=x.device) * noise_scale
        sequence = sequence + noise
        
        return sequence
        
    def forward(self, visual, audio, text):
        """
        前向传播
        
        参数:
            visual: 视觉特征 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, audio_dim]
            text: 文本特征 [batch_size, text_dim]
            
        返回:
            outputs: 各症状的分类结果列表
        """
        batch_size = visual.size(0)
        
        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 2. 准备序列数据
        visual_seq = self._prepare_sequence(visual_encoded)
        audio_seq = self._prepare_sequence(audio_encoded)
        text_seq = self._prepare_sequence(text_encoded)
        
        # 3. 应用BiLSTM编码器
        visual_feat, _ = self.visual_bilstm(visual_seq)
        audio_feat, _ = self.audio_bilstm(audio_seq)
        text_feat, _ = self.text_bilstm(text_seq)
        
        # 4. 计算自适应权重
        concat_feat = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
        modality_weights = self.adaptive_weight(concat_feat)
        
        # 5. 应用自适应权重
        weighted_visual = visual_feat * modality_weights[:, 0].unsqueeze(1)
        weighted_audio = audio_feat * modality_weights[:, 1].unsqueeze(1)
        weighted_text = text_feat * modality_weights[:, 2].unsqueeze(1)
        
        # 6. 特征融合
        weighted_concat = torch.cat([weighted_visual, weighted_audio, weighted_text], dim=1)
        fused_features = self.fusion(weighted_concat)
        
        # 7. 多症状分类
        outputs = [classifier(fused_features) for classifier in self.symptom_classifiers]
        
        return outputs

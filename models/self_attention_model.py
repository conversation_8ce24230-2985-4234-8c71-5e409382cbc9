import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class SelfAttention(nn.Module):
    """自注意力机制模块"""
    def __init__(self, hidden_dim, num_heads=4, dropout=0.1):
        super(SelfAttention, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        assert self.head_dim * num_heads == hidden_dim, "hidden_dim必须能被num_heads整除"
        
        # 投影层
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        
        # 输出层
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入特征 [batch_size, seq_len, hidden_dim]
            
        返回:
            output: 自注意力输出 [batch_size, seq_len, hidden_dim]
        """
        # 残差连接和层归一化
        residual = x
        x = self.norm1(x)
        
        batch_size, seq_len, _ = x.size()
        
        # 投影查询、键和值
        q = self.query(x)
        k = self.key(x)
        v = self.value(x)
        
        # 重塑为多头形式
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重
        context = torch.matmul(attn_weights, v)
        
        # 重塑回原始形状
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.hidden_dim)
        
        # 输出投影
        attn_output = self.out_proj(context)
        attn_output = self.dropout(attn_output)
        
        # 第一个残差连接
        out = residual + attn_output
        
        # 第二个残差连接和层归一化
        residual2 = out
        out = self.norm2(out)
        out = residual2 + self.ffn(out)
        
        return out

class SelfAttentionModel(nn.Module):
    """基于自注意力的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, num_heads=4, dropout=0.3):
        super(SelfAttentionModel, self).__init__()
        
        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 自注意力层 - 每个模态一个
        self.visual_self_attn = SelfAttention(hidden_dim, num_heads, dropout)
        self.audio_self_attn = SelfAttention(hidden_dim, num_heads, dropout)
        self.text_self_attn = SelfAttention(hidden_dim, num_heads, dropout)
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])
        
    def _prepare_sequence(self, x):
        """将静态特征转换为序列形式，以便进行自注意力处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)
        
        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x
            
        # 否则，创建一个假的序列 (长度为1的序列)
        sequence = x.unsqueeze(1)
        
        return sequence
        
    def forward(self, visual, audio, text):
        """
        前向传播
        
        参数:
            visual: 视觉特征 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, audio_dim]
            text: 文本特征 [batch_size, text_dim]
            
        返回:
            outputs: 各症状的分类结果列表
        """
        batch_size = visual.size(0)
        
        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 2. 准备序列数据
        visual_seq = self._prepare_sequence(visual_encoded)
        audio_seq = self._prepare_sequence(audio_encoded)
        text_seq = self._prepare_sequence(text_encoded)
        
        # 3. 应用自注意力
        visual_attended = self.visual_self_attn(visual_seq)
        audio_attended = self.audio_self_attn(audio_seq)
        text_attended = self.text_self_attn(text_seq)
        
        # 4. 提取特征 (如果是序列，取平均)
        visual_feat = visual_attended.mean(dim=1)
        audio_feat = audio_attended.mean(dim=1)
        text_feat = text_attended.mean(dim=1)
        
        # 5. 特征融合
        concat_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
        fused_features = self.fusion(concat_features)
        
        # 6. 多症状分类
        outputs = [classifier(fused_features) for classifier in self.symptom_classifiers]
        
        return outputs

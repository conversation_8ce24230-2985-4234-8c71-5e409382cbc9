import torch
import torch.nn as nn
import torch.nn.functional as F

class ModalityAttention(nn.Module):
    """模态注意力机制，用于PMR模型"""
    def __init__(self, hidden_dim, dropout=0.1):
        super(ModalityAttention, self).__init__()

        # 注意力层
        self.attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Softmax(dim=1)
        )

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入特征 [batch_size, num_modalities, hidden_dim]

        返回:
            weighted_sum: 加权和 [batch_size, hidden_dim]
            attention_weights: 注意力权重 [batch_size, num_modalities, 1]
        """
        # 计算注意力权重
        attention_weights = self.attention(x)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        weighted_sum = torch.sum(x * attention_weights, dim=1)

        return weighted_sum, attention_weights

class ProgressiveReasoning(nn.Module):
    """渐进式推理模块，用于PMR模型"""
    def __init__(self, hidden_dim, num_steps=3, dropout=0.1):
        super(ProgressiveReasoning, self).__init__()

        # 推理步骤
        self.reasoning_steps = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim * 2),
                nn.LayerNorm(hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim)
            ) for _ in range(num_steps)
        ])

        # 门控更新
        self.gates = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.Sigmoid()
            ) for _ in range(num_steps)
        ])

    def forward(self, x, context=None):
        """
        前向传播

        参数:
            x: 输入特征 [batch_size, hidden_dim]
            context: 上下文特征 [batch_size, hidden_dim]，如果为None则使用x

        返回:
            output: 推理输出 [batch_size, hidden_dim]
        """
        if context is None:
            context = x

        output = x

        # 渐进式推理
        for i, (reasoning_step, gate) in enumerate(zip(self.reasoning_steps, self.gates)):
            # 推理更新
            update = reasoning_step(output)

            # 门控机制
            gate_value = gate(torch.cat([output, context], dim=1))

            # 更新输出
            output = gate_value * update + (1 - gate_value) * output

        return output

class PMRModel(nn.Module):
    """渐进式多模态推理 (PMR) 模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256,
                 num_symptoms=4, num_reasoning_steps=3, dropout=0.3):
        super(PMRModel, self).__init__()

        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 模态注意力
        self.modality_attention = ModalityAttention(hidden_dim, dropout)

        # 渐进式推理模块
        self.visual_reasoning = ProgressiveReasoning(hidden_dim, num_reasoning_steps, dropout)
        self.audio_reasoning = ProgressiveReasoning(hidden_dim, num_reasoning_steps, dropout)
        self.text_reasoning = ProgressiveReasoning(hidden_dim, num_reasoning_steps, dropout)

        # 最终推理模块
        self.final_reasoning = ProgressiveReasoning(hidden_dim, num_reasoning_steps, dropout)

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 4, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])

    def forward(self, visual, audio, text):
        """
        前向传播

        参数:
            visual: 视觉特征 [batch_size, seq_len, visual_dim] 或 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, seq_len, audio_dim] 或 [batch_size, audio_dim]
            text: 文本特征 [batch_size, seq_len, text_dim] 或 [batch_size, text_dim]

        返回:
            outputs: 各症状的分类结果列表
        """
        # 处理输入维度
        if len(visual.shape) == 3:
            # 如果是序列数据，取平均值
            visual = visual.mean(dim=1)
            audio = audio.mean(dim=1)
            text = text.mean(dim=1)

        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)

        # 2. 模态注意力
        modality_features = torch.stack([visual_encoded, audio_encoded, text_encoded], dim=1)
        attended_features, attention_weights = self.modality_attention(modality_features)

        # 3. 单模态渐进式推理
        visual_reasoned = self.visual_reasoning(visual_encoded, attended_features)
        audio_reasoned = self.audio_reasoning(audio_encoded, attended_features)
        text_reasoned = self.text_reasoning(text_encoded, attended_features)

        # 4. 多模态融合
        concat_features = torch.cat([
            visual_reasoned, audio_reasoned, text_reasoned, attended_features
        ], dim=1)

        fused_features = self.fusion(concat_features)

        # 5. 最终推理
        final_features = self.final_reasoning(fused_features)

        # 6. 多症状分类
        outputs = [classifier(final_features) for classifier in self.symptom_classifiers]

        return outputs

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class PositionalEncoding(nn.Module):
    """位置编码，用于Transformer模型"""
    def __init__(self, d_model, max_len=100):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        x = x + self.pe[:, :x.size(1), :]
        return x

class CrossmodalAttention(nn.Module):
    """跨模态注意力模块，用于MulT模型"""
    def __init__(self, in_dim, out_dim, num_heads=4, dropout=0.1):
        super(CrossmodalAttention, self).__init__()
        self.num_heads = num_heads
        self.out_dim = out_dim
        self.head_dim = out_dim // num_heads
        assert self.head_dim * num_heads == out_dim, "out_dim必须能被num_heads整除"

        # 投影层
        self.query_proj = nn.Linear(in_dim, out_dim)
        self.key_proj = nn.Linear(in_dim, out_dim)
        self.value_proj = nn.Linear(in_dim, out_dim)

        # 输出层
        self.out_proj = nn.Linear(out_dim, out_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

        # 层归一化
        self.norm1 = nn.LayerNorm(out_dim)
        self.norm2 = nn.LayerNorm(out_dim)

        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(out_dim, out_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(out_dim * 4, out_dim),
            nn.Dropout(dropout)
        )

    def forward(self, query, key, value):
        """
        前向传播

        参数:
            query: 查询特征 [batch_size, seq_len_q, in_dim]
            key: 键特征 [batch_size, seq_len_k, in_dim]
            value: 值特征 [batch_size, seq_len_v, in_dim]

        返回:
            output: 注意力输出 [batch_size, seq_len_q, out_dim]
        """
        residual = query

        batch_size, seq_len_q, _ = query.size()
        seq_len_k = key.size(1)

        # 投影查询、键和值
        q = self.query_proj(query)
        k = self.key_proj(key)
        v = self.value_proj(value)

        # 重塑为多头形式
        q = q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # 应用注意力权重
        context = torch.matmul(attn_weights, v)

        # 重塑回原始形状
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len_q, self.out_dim)

        # 输出投影
        attn_output = self.out_proj(context)
        attn_output = self.dropout(attn_output)

        # 第一个残差连接
        out = self.norm1(residual + attn_output)

        # 第二个残差连接和层归一化
        residual2 = out
        out = self.norm2(residual2 + self.ffn(out))

        return out

class TransformerEncoder(nn.Module):
    """Transformer编码器，用于单模态特征处理"""
    def __init__(self, input_dim, hidden_dim, num_layers=2, num_heads=4, dropout=0.1):
        super(TransformerEncoder, self).__init__()

        # 位置编码
        self.pos_encoder = PositionalEncoding(hidden_dim)

        # 特征投影
        self.input_proj = nn.Linear(input_dim, hidden_dim)

        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入特征 [batch_size, seq_len, input_dim]

        返回:
            output: Transformer编码器输出 [batch_size, seq_len, hidden_dim]
        """
        # 检查输入维度
        if len(x.shape) == 3:
            batch_size, seq_len, feat_dim = x.shape
            # 如果特征维度与输入维度不匹配，先进行reshape和投影
            if feat_dim != self.input_proj.in_features:
                # 重塑为2D张量进行线性投影
                x_flat = x.reshape(-1, feat_dim)
                x_proj = self.input_proj(x_flat)
                # 重塑回3D张量
                x = x_proj.reshape(batch_size, seq_len, -1)
            else:
                # 特征投影
                x = self.input_proj(x)
        else:
            # 处理2D输入
            x = self.input_proj(x.unsqueeze(1)).squeeze(1)

        # 位置编码
        x = self.pos_encoder(x)

        # Transformer编码器
        output = self.transformer_encoder(x)

        return output

class MulTModel(nn.Module):
    """多模态Transformer (MulT) 模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256,
                 num_symptoms=4, num_layers=2, num_heads=4, dropout=0.3):
        super(MulTModel, self).__init__()

        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 单模态Transformer编码器
        self.visual_transformer = TransformerEncoder(hidden_dim, hidden_dim, num_layers, num_heads, dropout)
        self.audio_transformer = TransformerEncoder(hidden_dim, hidden_dim, num_layers, num_heads, dropout)
        self.text_transformer = TransformerEncoder(hidden_dim, hidden_dim, num_layers, num_heads, dropout)

        # 跨模态注意力层
        self.v2a_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)
        self.v2t_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)
        self.a2v_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)
        self.a2t_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)
        self.t2v_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)
        self.t2a_crossmodal = CrossmodalAttention(hidden_dim, hidden_dim, num_heads, dropout)

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])

    def _prepare_sequence(self, x, seq_len=10):
        """将静态特征转换为序列形式，以便进行Transformer处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)

        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x

        # 否则，创建一个假的序列
        # 通过添加少量噪声创建序列变化
        noise_scale = 0.05
        sequence = x.unsqueeze(1).expand(batch_size, seq_len, feature_dim)
        noise = torch.randn(batch_size, seq_len, feature_dim, device=x.device) * noise_scale
        sequence = sequence + noise

        return sequence

    def forward(self, visual, audio, text):
        """
        前向传播

        参数:
            visual: 视觉特征 [batch_size, seq_len, visual_dim] 或 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, seq_len, audio_dim] 或 [batch_size, audio_dim]
            text: 文本特征 [batch_size, seq_len, text_dim] 或 [batch_size, text_dim]

        返回:
            outputs: 各症状的分类结果列表
        """
        batch_size = visual.size(0)

        # 1. 处理序列数据
        if len(visual.shape) == 3:
            # 已经是序列数据，直接使用
            seq_len = visual.size(1)

            # 对每个时间步应用编码器
            visual_flat = visual.reshape(-1, visual.size(-1))
            audio_flat = audio.reshape(-1, audio.size(-1))
            text_flat = text.reshape(-1, text.size(-1))

            visual_encoded_flat = self.visual_encoder(visual_flat)
            audio_encoded_flat = self.audio_encoder(audio_flat)
            text_encoded_flat = self.text_encoder(text_flat)

            # 重塑回序列形式
            visual_encoded_seq = visual_encoded_flat.reshape(batch_size, seq_len, -1)
            audio_encoded_seq = audio_encoded_flat.reshape(batch_size, seq_len, -1)
            text_encoded_seq = text_encoded_flat.reshape(batch_size, seq_len, -1)

            # 使用编码后的序列
            visual_seq = visual_encoded_seq
            audio_seq = audio_encoded_seq
            text_seq = text_encoded_seq
        else:
            # 静态特征处理
            visual_encoded = self.visual_encoder(visual)
            audio_encoded = self.audio_encoder(audio)
            text_encoded = self.text_encoder(text)

            # 准备序列数据
            visual_seq = self._prepare_sequence(visual_encoded)
            audio_seq = self._prepare_sequence(audio_encoded)
            text_seq = self._prepare_sequence(text_encoded)

        # 2. 单模态Transformer编码
        try:
            visual_trans = self.visual_transformer(visual_seq)
            audio_trans = self.audio_transformer(audio_seq)
            text_trans = self.text_transformer(text_seq)
        except Exception as e:
            # 如果出错，尝试直接使用编码后的特征
            print(f"Transformer编码出错: {e}")
            # 使用平均池化作为备选方案
            visual_feat = torch.mean(visual_seq, dim=1)
            audio_feat = torch.mean(audio_seq, dim=1)
            text_feat = torch.mean(text_seq, dim=1)

            # 跳到特征融合步骤
            concat_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
            fused_features = self.fusion(concat_features)

            # 多症状分类
            outputs = []
            for classifier in self.symptom_classifiers:
                logits = classifier(fused_features)
                # 添加一个小的偏置项，使模型不总是预测正类
                logits[:, 0] = logits[:, 0] + 0.2
                outputs.append(logits)

            return outputs

        # 3. 跨模态注意力
        try:
            v2a_out = self.v2a_crossmodal(visual_trans, audio_trans, audio_trans)
            v2t_out = self.v2t_crossmodal(visual_trans, text_trans, text_trans)
            a2v_out = self.a2v_crossmodal(audio_trans, visual_trans, visual_trans)
            a2t_out = self.a2t_crossmodal(audio_trans, text_trans, text_trans)
            t2v_out = self.t2v_crossmodal(text_trans, visual_trans, visual_trans)
            t2a_out = self.t2a_crossmodal(text_trans, audio_trans, audio_trans)
        except Exception as e:
            # 如果跨模态注意力出错，使用原始特征
            print(f"跨模态注意力出错: {e}")
            visual_feat = torch.mean(visual_trans, dim=1)
            audio_feat = torch.mean(audio_trans, dim=1)
            text_feat = torch.mean(text_trans, dim=1)

            # 跳到特征融合步骤
            concat_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
            fused_features = self.fusion(concat_features)

            # 多症状分类
            outputs = []
            for classifier in self.symptom_classifiers:
                logits = classifier(fused_features)
                # 添加一个小的偏置项，使模型不总是预测正类
                logits[:, 0] = logits[:, 0] + 0.2
                outputs.append(logits)

            return outputs

        # 4. 提取特征 (加权平均)
        visual_feat = (visual_trans * 0.5 + a2v_out * 0.25 + t2v_out * 0.25).mean(dim=1)
        audio_feat = (audio_trans * 0.5 + v2a_out * 0.25 + t2a_out * 0.25).mean(dim=1)
        text_feat = (text_trans * 0.5 + v2t_out * 0.25 + a2t_out * 0.25).mean(dim=1)

        # 5. 特征融合
        concat_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
        fused_features = self.fusion(concat_features)

        # 6. 多症状分类 - 确保输出更平衡
        outputs = []
        for classifier in self.symptom_classifiers:
            logits = classifier(fused_features)
            # 添加一个小的偏置项，使模型不总是预测正类
            logits[:, 0] = logits[:, 0] + 0.2
            outputs.append(logits)

        return outputs

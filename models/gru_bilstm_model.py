import torch
import torch.nn as nn
import torch.nn.functional as F

class GRUBiLSTMEncoder(nn.Module):
    """GRU-BiLSTM编码器，先使用GRU处理序列，再使用BiLSTM进一步提取特征"""
    def __init__(self, input_dim, hidden_dim, num_layers=2, dropout=0.2):
        super(GRUBiLSTMEncoder, self).__init__()

        # 确保hidden_dim是4的倍数，以便于后续的维度划分
        assert hidden_dim % 4 == 0, "hidden_dim必须是4的倍数"

        # GRU层
        self.gru = nn.GRU(
            input_dim,
            hidden_dim // 2,
            num_layers=num_layers // 2 if num_layers > 1 else 1,
            batch_first=True,
            dropout=dropout if num_layers > 2 else 0,
            bidirectional=False
        )

        # BiLSTM层
        self.bilstm = nn.LSTM(
            hidden_dim // 2,
            hidden_dim // 4,
            num_layers=num_layers // 2 if num_layers > 1 else 1,
            batch_first=True,
            dropout=dropout if num_layers > 2 else 0,
            bidirectional=True
        )

        # 层归一化 - 用于LSTM输出序列
        self.seq_norm = nn.LayerNorm(hidden_dim // 2)

        # 投影层 - 将连接后的隐藏状态投影到hidden_dim维度
        self.projection = nn.Linear(hidden_dim // 2, hidden_dim)

        # 层归一化 - 用于投影后的隐藏状态
        self.hidden_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入序列 [batch_size, seq_len, input_dim]

        返回:
            output: 最终隐藏状态 [batch_size, hidden_dim]
            seq_output: 序列输出 [batch_size, seq_len, hidden_dim//2]
        """
        # GRU处理
        gru_out, _ = self.gru(x)

        # BiLSTM处理
        lstm_out, (hidden, _) = self.bilstm(gru_out)

        # 连接前向和后向的最后隐藏状态 [batch_size, hidden_dim//2]
        last_hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)

        # 应用层归一化到序列输出
        lstm_out = self.seq_norm(lstm_out)

        # 投影隐藏状态到hidden_dim维度
        projected_hidden = self.projection(last_hidden)

        # 应用层归一化到投影后的隐藏状态
        projected_hidden = self.hidden_norm(projected_hidden)

        return projected_hidden, lstm_out

class GRUBiLSTMModel(nn.Module):
    """基于GRU-BiLSTM的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, num_symptoms=4, num_layers=4, dropout=0.3):
        super(GRUBiLSTMModel, self).__init__()

        # 特征编码器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # GRU-BiLSTM编码器
        self.visual_gru_bilstm = GRUBiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)
        self.audio_gru_bilstm = GRUBiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)
        self.text_gru_bilstm = GRUBiLSTMEncoder(hidden_dim, hidden_dim, num_layers, dropout)

        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(hidden_dim, num_heads=4, dropout=dropout, batch_first=True)

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 多症状分类器
        self.symptom_classifiers = nn.ModuleList([
            nn.Linear(hidden_dim, 2) for _ in range(num_symptoms)
        ])

    def _prepare_sequence(self, x, seq_len=10):
        """将静态特征转换为序列形式，以便进行时序处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)

        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x

        # 否则，创建一个假的序列
        # 通过添加少量噪声创建序列变化
        noise_scale = 0.05
        sequence = x.unsqueeze(1).expand(batch_size, seq_len, feature_dim)
        noise = torch.randn(batch_size, seq_len, feature_dim, device=x.device) * noise_scale
        sequence = sequence + noise

        return sequence

    def forward(self, visual, audio, text):
        """
        前向传播

        参数:
            visual: 视觉特征 [batch_size, visual_dim]
            audio: 音频特征 [batch_size, audio_dim]
            text: 文本特征 [batch_size, text_dim]

        返回:
            outputs: 各症状的分类结果列表
        """
        batch_size = visual.size(0)

        # 1. 特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)

        # 2. 准备序列数据
        visual_seq = self._prepare_sequence(visual_encoded)
        audio_seq = self._prepare_sequence(audio_encoded)
        text_seq = self._prepare_sequence(text_encoded)

        # 3. 应用GRU-BiLSTM编码器
        visual_feat, visual_seq_out = self.visual_gru_bilstm(visual_seq)
        audio_feat, audio_seq_out = self.audio_gru_bilstm(audio_seq)
        text_feat, text_seq_out = self.text_gru_bilstm(text_seq)

        # 4. 应用跨模态注意力 (可选)
        # 创建投影层
        visual_projection = nn.Linear(visual_seq_out.size(-1), self.visual_encoder[0].out_features).to(visual_seq_out.device)
        audio_projection = nn.Linear(audio_seq_out.size(-1), self.audio_encoder[0].out_features).to(audio_seq_out.device)
        text_projection = nn.Linear(text_seq_out.size(-1), self.text_encoder[0].out_features).to(text_seq_out.device)

        # 将序列输出投影到相同维度
        visual_proj = visual_projection(visual_seq_out)
        audio_proj = audio_projection(audio_seq_out)
        text_proj = text_projection(text_seq_out)

        # 将三个模态的序列输出堆叠在一起
        stacked_features = torch.cat([
            visual_proj, audio_proj, text_proj
        ], dim=1)  # [batch_size, seq_len*3, hidden_dim]

        # 自注意力处理 - 这里我们不使用返回的特征，只是为了保持代码结构
        _ = self.cross_attention(
            stacked_features, stacked_features, stacked_features
        )

        # 5. 特征融合
        concat_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
        fused_features = self.fusion(concat_features)

        # 6. 多症状分类
        outputs = [classifier(fused_features) for classifier in self.symptom_classifiers]

        return outputs

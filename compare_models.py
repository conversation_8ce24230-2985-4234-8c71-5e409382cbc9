import json
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse

def load_metrics(file_path):
    """加载评估指标"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def compare_models(baseline_metrics, temporal_metrics, hierarchical_metrics, output_dir):
    """比较不同模型的性能"""
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状', 'average']
    metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    
    # 创建比较表格
    comparison = {}
    
    for symptom in symptom_types:
        comparison[symptom] = {}
        
        for metric in metrics:
            if symptom in baseline_metrics and metric in baseline_metrics[symptom]:
                baseline_value = baseline_metrics[symptom][metric]
            else:
                baseline_value = 0.0
            
            if symptom in temporal_metrics and metric in temporal_metrics[symptom]:
                temporal_value = temporal_metrics[symptom][metric]
            else:
                temporal_value = 0.0
            
            if symptom in hierarchical_metrics and metric in hierarchical_metrics[symptom]:
                hierarchical_value = hierarchical_metrics[symptom][metric]
            else:
                hierarchical_value = 0.0
            
            comparison[symptom][metric] = {
                'baseline': baseline_value,
                'temporal': temporal_value,
                'hierarchical': hierarchical_value,
                'temporal_improvement': temporal_value - baseline_value,
                'hierarchical_improvement': hierarchical_value - baseline_value
            }
    
    # 打印比较结果
    print("模型性能比较:")
    for symptom in symptom_types:
        print(f"\n{symptom}:")
        for metric in metrics:
            baseline = comparison[symptom][metric]['baseline']
            temporal = comparison[symptom][metric]['temporal']
            hierarchical = comparison[symptom][metric]['hierarchical']
            temporal_imp = comparison[symptom][metric]['temporal_improvement']
            hierarchical_imp = comparison[symptom][metric]['hierarchical_improvement']
            
            print(f"  {metric.capitalize()}:")
            print(f"    基准模型: {baseline:.4f}")
            print(f"    时序增强模型: {temporal:.4f} ({'+' if temporal_imp >= 0 else ''}{temporal_imp:.4f})")
            print(f"    分层分类模型: {hierarchical:.4f} ({'+' if hierarchical_imp >= 0 else ''}{hierarchical_imp:.4f})")
    
    # 保存比较结果
    with open(os.path.join(output_dir, 'model_comparison.json'), 'w', encoding='utf-8') as f:
        json.dump(comparison, f, ensure_ascii=False, indent=4)
    
    # 绘制比较图表
    plot_comparison(comparison, output_dir)
    
    return comparison

def plot_comparison(comparison, output_dir):
    """绘制模型比较图表"""
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状', 'average']
    metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    
    # 创建输出目录
    os.makedirs(os.path.join(output_dir, 'plots'), exist_ok=True)
    
    # 绘制每个指标的比较图
    for metric in metrics:
        plt.figure(figsize=(12, 8))
        
        x = np.arange(len(symptom_types))
        width = 0.25
        
        baseline_values = [comparison[symptom][metric]['baseline'] for symptom in symptom_types]
        temporal_values = [comparison[symptom][metric]['temporal'] for symptom in symptom_types]
        hierarchical_values = [comparison[symptom][metric]['hierarchical'] for symptom in symptom_types]
        
        plt.bar(x - width, baseline_values, width, label='基准模型')
        plt.bar(x, temporal_values, width, label='时序增强模型')
        plt.bar(x + width, hierarchical_values, width, label='分层分类模型')
        
        plt.xlabel('症状类型')
        plt.ylabel(f'{metric.capitalize()}')
        plt.title(f'不同模型的{metric.capitalize()}比较')
        plt.xticks(x, symptom_types)
        plt.legend()
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # 添加数值标签
        for i, v in enumerate(baseline_values):
            plt.text(i - width, v + 0.01, f'{v:.2f}', ha='center', va='bottom', fontsize=8)
        for i, v in enumerate(temporal_values):
            plt.text(i, v + 0.01, f'{v:.2f}', ha='center', va='bottom', fontsize=8)
        for i, v in enumerate(hierarchical_values):
            plt.text(i + width, v + 0.01, f'{v:.2f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'plots', f'{metric}_comparison.png'), dpi=300)
        plt.close()
    
    # 绘制召回率提升图
    plt.figure(figsize=(10, 6))
    
    x = np.arange(len(symptom_types))
    width = 0.35
    
    temporal_improvements = [comparison[symptom]['recall']['temporal_improvement'] for symptom in symptom_types]
    hierarchical_improvements = [comparison[symptom]['recall']['hierarchical_improvement'] for symptom in symptom_types]
    
    plt.bar(x - width/2, temporal_improvements, width, label='时序增强模型')
    plt.bar(x + width/2, hierarchical_improvements, width, label='分层分类模型')
    
    plt.xlabel('症状类型')
    plt.ylabel('召回率提升')
    plt.title('不同模型相对于基准模型的召回率提升')
    plt.xticks(x, symptom_types)
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, v in enumerate(temporal_improvements):
        plt.text(i - width/2, v + 0.01 if v >= 0 else v - 0.03, f'{v:.2f}', ha='center', va='bottom' if v >= 0 else 'top', fontsize=8)
    for i, v in enumerate(hierarchical_improvements):
        plt.text(i + width/2, v + 0.01 if v >= 0 else v - 0.03, f'{v:.2f}', ha='center', va='bottom' if v >= 0 else 'top', fontsize=8)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'plots', 'recall_improvement.png'), dpi=300)
    plt.close()

def main():
    parser = argparse.ArgumentParser(description="比较不同模型的性能")
    parser.add_argument("--baseline_metrics", type=str, default="./cross_modal_consistency_test_results.json", help="基准模型的评估指标文件")
    parser.add_argument("--temporal_metrics", type=str, default="./temporal_results/temporal_test_results.json", help="时序增强模型的评估指标文件")
    parser.add_argument("--hierarchical_metrics", type=str, default="./hierarchical_results/hierarchical_test_results.json", help="分层分类模型的评估指标文件")
    parser.add_argument("--output_dir", type=str, default="./comparison_results", help="输出目录")
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载评估指标
    baseline_metrics = load_metrics(args.baseline_metrics)
    temporal_metrics = load_metrics(args.temporal_metrics)
    hierarchical_metrics = load_metrics(args.hierarchical_metrics)
    
    # 比较模型性能
    compare_models(baseline_metrics, temporal_metrics, hierarchical_metrics, args.output_dir)

if __name__ == "__main__":
    main()

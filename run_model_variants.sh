#!/bin/bash

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 创建结果目录
mkdir -p ablation_results

# 运行各个模型变体的实验
echo "开始运行模型变体实验..."

# 运行GRU-BiLSTM模型
echo "运行GRU-BiLSTM模型..."
python train_model_variants.py --model_type gru_bilstm --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行Bi-LSTM模型
echo "运行Bi-LSTM模型..."
python train_model_variants.py --model_type bilstm --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行MulT模型
echo "运行MulT模型..."
python train_model_variants.py --model_type mult --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行PMR模型
echo "运行PMR模型..."
python train_model_variants.py --model_type pmr --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行Self-attention模型
echo "运行Self-attention模型..."
python train_model_variants.py --model_type self_attention --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行Cross-attention模型
echo "运行Cross-attention模型..."
python train_model_variants.py --model_type cross_attention --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 运行CubeMLP模型
echo "运行CubeMLP模型..."
python train_model_variants.py --model_type cube_mlp --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss

# 比较模型变体结果
echo "比较模型变体结果..."
python compare_model_variants.py --update_comprehensive

echo "所有实验完成！"

#!/bin/bash

# 设置数据目录和标签文件
DATA_DIR="./shuju"
LABEL_FILE="./symptom_label_template.xlsx"

# 创建输出目录
mkdir -p ./ablation_results

# 运行完整模型
echo "运行完整模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 0

# 运行仅时序增强模型
echo "运行仅时序增强模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 1

# 运行仅跨模态一致性模型
echo "运行仅跨模态一致性模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 2

# 运行无自适应权重模型
echo "运行无自适应权重模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 3

# 运行无分层分类模型
echo "运行无分层分类模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 4

# 运行基础模型
echo "运行基础模型..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --batch_size 32 \
    --epochs 50 \
    --learning_rate 0.0005 \
    --patience 10 \
    --max_seq_len 100 \
    --hidden_dim 256 \
    --augment_prob 0.2 \
    --output_dir ./ablation_results \
    --config_index 5

# 生成比较报告
echo "生成比较报告..."
python ablation_experiments.py \
    --data_dir $DATA_DIR \
    --label_file $LABEL_FILE \
    --output_dir ./ablation_results

echo "所有消融实验完成！"

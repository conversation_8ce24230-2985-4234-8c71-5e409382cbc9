import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from temporal_enhanced_model import PositionalEncoding, TemporalAttention, LSTMEncoder, CrossModalTemporalAttention, HierarchicalClassifier

class AblationTemporalModel(nn.Module):
    """用于消融实验的时序增强多模态模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, max_seq_len=100,
                 use_temporal=True, use_cross_modal=True, use_adaptive_weight=True, use_hierarchical=True):
        super(AblationTemporalModel, self).__init__()

        # 保存配置
        self.use_temporal = use_temporal
        self.use_cross_modal = use_cross_modal
        self.use_adaptive_weight = use_adaptive_weight
        self.use_hierarchical = use_hierarchical

        # 位置编码 - 仅在使用时序增强时使用
        if use_temporal:
            self.visual_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)
            self.audio_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)
            self.text_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)

        # 特征编码器 - 静态特征
        self.visual_encoder = nn.Sequential(
            nn.Linear(128, hidden_dim),  # 固定视觉特征维度为128
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        self.audio_encoder = nn.Sequential(
            nn.Linear(128, hidden_dim),  # 固定音频特征维度为128
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        self.text_encoder = nn.Sequential(
            nn.Linear(768, hidden_dim),  # 固定文本特征维度为768
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # 时序编码器 - 仅在使用时序增强时使用
        if use_temporal:
            self.visual_lstm = LSTMEncoder(hidden_dim, hidden_dim)
            self.audio_lstm = LSTMEncoder(hidden_dim, hidden_dim)
            self.text_lstm = LSTMEncoder(hidden_dim, hidden_dim)

            self.visual_temporal_attn = TemporalAttention(hidden_dim)
            self.audio_temporal_attn = TemporalAttention(hidden_dim)
            self.text_temporal_attn = TemporalAttention(hidden_dim)

        # 跨模态时序注意力 - 仅在使用跨模态一致性时使用
        if use_cross_modal:
            self.v2a_attn = CrossModalTemporalAttention(hidden_dim)
            self.v2t_attn = CrossModalTemporalAttention(hidden_dim)
            self.a2v_attn = CrossModalTemporalAttention(hidden_dim)
            self.a2t_attn = CrossModalTemporalAttention(hidden_dim)
            self.t2v_attn = CrossModalTemporalAttention(hidden_dim)
            self.t2a_attn = CrossModalTemporalAttention(hidden_dim)

        # 融合门控 - 仅在使用自适应权重时使用
        if use_adaptive_weight:
            if use_cross_modal:
                # 完整模型的融合门控
                self.fusion_gate = nn.Sequential(
                    nn.Linear(hidden_dim * 9, hidden_dim * 3),
                    nn.LayerNorm(hidden_dim * 3),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_dim * 3, hidden_dim * 3),
                    nn.Sigmoid()
                )
            else:
                # 无跨模态一致性时的融合门控
                self.fusion_gate = nn.Sequential(
                    nn.Linear(hidden_dim * 3, hidden_dim * 3),
                    nn.LayerNorm(hidden_dim * 3),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_dim * 3, hidden_dim * 3),
                    nn.Sigmoid()
                )

        # 融合层 - 根据是否使用跨模态一致性和自适应权重调整输入维度
        if use_cross_modal and use_adaptive_weight:
            # 使用跨模态一致性和自适应权重时，输入维度为 hidden_dim * 9
            self.fusion = nn.Sequential(
                nn.Linear(hidden_dim * 9, hidden_dim * 2),
                nn.LayerNorm(hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU()
            )
        elif use_cross_modal and not use_adaptive_weight:
            # 使用跨模态一致性但不使用自适应权重时，输入维度为 hidden_dim * 3
            self.fusion = nn.Sequential(
                nn.Linear(hidden_dim * 3, hidden_dim * 2),
                nn.LayerNorm(hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU()
            )
        else:
            # 不使用跨模态一致性时，输入维度为 hidden_dim * 3
            self.fusion = nn.Sequential(
                nn.Linear(hidden_dim * 3, hidden_dim * 2),
                nn.LayerNorm(hidden_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU()
            )

        # 残差连接
        self.residual = nn.Linear(hidden_dim * 3, hidden_dim)

        # 分类器 - 根据是否使用分层分类选择不同的分类器
        if use_hierarchical:
            # 分层分类器
            self.symptom_classifiers = nn.ModuleDict({
                '情绪症状': HierarchicalClassifier(hidden_dim),
                '躯体症状': HierarchicalClassifier(hidden_dim),
                '认知症状': HierarchicalClassifier(hidden_dim),
                '行为症状': HierarchicalClassifier(hidden_dim)
            })
        else:
            # 普通分类器
            self.symptom_classifiers = nn.ModuleDict({
                '情绪症状': nn.Linear(hidden_dim, 2),
                '躯体症状': nn.Linear(hidden_dim, 2),
                '认知症状': nn.Linear(hidden_dim, 2),
                '行为症状': nn.Linear(hidden_dim, 2)
            })

        # 症状特定注意力
        self.symptom_attentions = nn.ModuleDict({
            '情绪症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '躯体症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '认知症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '行为症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            )
        })

    def _prepare_sequence(self, x, seq_len=10):
        """将静态特征转换为序列形式，以便进行时序处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)

        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x

        # 否则，创建一个假的序列
        # 通过添加少量噪声创建序列变化
        noise_scale = 0.05
        sequence = x.unsqueeze(1).expand(batch_size, seq_len, feature_dim)
        noise = torch.randn(batch_size, seq_len, feature_dim, device=x.device) * noise_scale
        sequence = sequence + noise

        return sequence

    def forward(self, visual, audio, text, training=True):
        batch_size = visual.size(0)

        # 1. 静态特征编码
        batch_size = visual.size(0)
        seq_len = visual.size(1)

        # 处理序列输入 [batch_size, seq_len, feature_dim]
        if len(visual.shape) == 3:
            # 将序列展平为 [batch_size * seq_len, feature_dim]
            visual_flat = visual.view(-1, visual.size(2))
            audio_flat = audio.view(-1, audio.size(2))
            text_flat = text.view(-1, text.size(2))

            # 编码特征
            visual_encoded_flat = self.visual_encoder(visual_flat)
            audio_encoded_flat = self.audio_encoder(audio_flat)
            text_encoded_flat = self.text_encoder(text_flat)

            # 重塑回序列形式 [batch_size, seq_len, hidden_dim]
            visual_encoded = visual_encoded_flat.view(batch_size, seq_len, -1)
            audio_encoded = audio_encoded_flat.view(batch_size, seq_len, -1)
            text_encoded = text_encoded_flat.view(batch_size, seq_len, -1)
        else:
            # 如果输入不是序列，直接编码
            visual_encoded = self.visual_encoder(visual)
            audio_encoded = self.audio_encoder(audio)
            text_encoded = self.text_encoder(text)

        # 根据配置选择不同的处理流程
        if self.use_temporal:
            # 已经是序列数据，直接使用
            visual_seq = visual_encoded
            audio_seq = audio_encoded
            text_seq = text_encoded

            # 3. 添加位置编码
            visual_seq = self.visual_pos_encoder(visual_seq)
            audio_seq = self.audio_pos_encoder(audio_seq)
            text_seq = self.text_pos_encoder(text_seq)

            # 4. 时序编码 - LSTM
            visual_lstm_out, visual_seq_out = self.visual_lstm(visual_seq)
            audio_lstm_out, audio_seq_out = self.audio_lstm(audio_seq)
            text_lstm_out, text_seq_out = self.text_lstm(text_seq)

            # 5. 时序注意力
            visual_temporal = self.visual_temporal_attn(visual_seq_out)
            audio_temporal = self.audio_temporal_attn(audio_seq_out)
            text_temporal = self.text_temporal_attn(text_seq_out)

            # 使用时序特征
            visual_feat = visual_lstm_out
            audio_feat = audio_lstm_out
            text_feat = text_lstm_out

            if self.use_cross_modal:
                # 6. 跨模态时序注意力
                v2a_out, _ = self.v2a_attn(visual_temporal, audio_temporal, audio_temporal)
                v2t_out, _ = self.v2t_attn(visual_temporal, text_temporal, text_temporal)
                a2v_out, _ = self.a2v_attn(audio_temporal, visual_temporal, visual_temporal)
                a2t_out, _ = self.a2t_attn(audio_temporal, text_temporal, text_temporal)
                t2v_out, _ = self.t2v_attn(text_temporal, visual_temporal, visual_temporal)
                t2a_out, _ = self.t2a_attn(text_temporal, audio_temporal, audio_temporal)

                # 7. 提取每个序列的表示
                v2a_feat = torch.mean(v2a_out, dim=1)
                v2t_feat = torch.mean(v2t_out, dim=1)
                a2v_feat = torch.mean(a2v_out, dim=1)
                a2t_feat = torch.mean(a2t_out, dim=1)
                t2v_feat = torch.mean(t2v_out, dim=1)
                t2a_feat = torch.mean(t2a_out, dim=1)

                # 8. 融合特征 - 包含所有交叉注意力结果
                concat_feat = torch.cat([
                    visual_feat, audio_feat, text_feat,
                    v2a_feat, v2t_feat, a2v_feat, a2t_feat, t2v_feat, t2a_feat
                ], dim=1)
            else:
                # 无跨模态一致性时，只使用时序特征
                concat_feat = torch.cat([visual_feat, audio_feat, text_feat], dim=1)
        else:
            # 不使用时序增强，直接使用静态特征
            # 提取每个序列的表示（使用平均池化）
            visual_feat = torch.mean(visual_encoded, dim=1)
            audio_feat = torch.mean(audio_encoded, dim=1)
            text_feat = torch.mean(text_encoded, dim=1)

            if self.use_cross_modal:
                # 使用跨模态注意力，但不使用时序
                # 已经是序列数据，直接使用
                visual_seq = visual_encoded
                audio_seq = audio_encoded
                text_seq = text_encoded

                # 简化的跨模态注意力
                v2a_out, _ = self.v2a_attn(visual_seq, audio_seq, audio_seq)
                v2t_out, _ = self.v2t_attn(visual_seq, text_seq, text_seq)
                a2v_out, _ = self.a2v_attn(audio_seq, visual_seq, visual_seq)
                a2t_out, _ = self.a2t_attn(audio_seq, text_seq, text_seq)
                t2v_out, _ = self.t2v_attn(text_seq, visual_seq, visual_seq)
                t2a_out, _ = self.t2a_attn(text_seq, audio_seq, audio_seq)

                # 提取特征（使用平均池化）
                v2a_feat = torch.mean(v2a_out, dim=1)
                v2t_feat = torch.mean(v2t_out, dim=1)
                a2v_feat = torch.mean(a2v_out, dim=1)
                a2t_feat = torch.mean(a2t_out, dim=1)
                t2v_feat = torch.mean(t2v_out, dim=1)
                t2a_feat = torch.mean(t2a_out, dim=1)

                # 提取每个序列的表示（使用平均池化）
                visual_feat = torch.mean(visual_seq, dim=1)
                audio_feat = torch.mean(audio_seq, dim=1)
                text_feat = torch.mean(text_seq, dim=1)

                # 融合特征
                concat_feat = torch.cat([
                    visual_feat, audio_feat, text_feat,
                    v2a_feat, v2t_feat, a2v_feat, a2t_feat, t2v_feat, t2a_feat
                ], dim=1)
            else:
                # 既不使用时序增强也不使用跨模态一致性
                concat_feat = torch.cat([visual_feat, audio_feat, text_feat], dim=1)

        # 9. 应用自适应门控或简单融合
        if self.use_adaptive_weight:
            gate = self.fusion_gate(concat_feat)
            hidden_dim = visual_feat.size(1)
            gated_features = torch.cat([
                visual_feat * gate[:, :hidden_dim],
                audio_feat * gate[:, hidden_dim:2*hidden_dim],
                text_feat * gate[:, 2*hidden_dim:3*hidden_dim]
            ], dim=1)
        else:
            # 不使用自适应权重，简单连接特征
            gated_features = torch.cat([visual_feat, audio_feat, text_feat], dim=1)

        # 10. 最终融合
        fused = self.fusion(gated_features)

        # 11. 残差连接
        residual_features = self.residual(torch.cat([visual_feat, audio_feat, text_feat], dim=1))
        fused = fused + residual_features

        # 12. 症状特化分类
        outputs = {}
        for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']:
            # 应用症状特定注意力
            symptom_attn = self.symptom_attentions[symptom](fused)
            symptom_features = fused * symptom_attn

            # 根据是否使用分层分类选择不同的分类方式
            if self.use_hierarchical:
                # 分层分类
                if training:
                    # 训练时返回两个分类器的logits
                    presence_logits, type_logits = self.symptom_classifiers[symptom](symptom_features, training=True)
                    outputs[symptom] = (presence_logits, type_logits)
                else:
                    # 推理时返回最终预测
                    final_probs = self.symptom_classifiers[symptom](symptom_features, training=False)
                    outputs[symptom] = final_probs
            else:
                # 普通分类
                logits = self.symptom_classifiers[symptom](symptom_features)
                if training:
                    outputs[symptom] = logits
                else:
                    probs = F.softmax(logits, dim=1)[:, 1].unsqueeze(1)  # 取正类概率
                    outputs[symptom] = probs

        # 13. 转换输出为列表，保持与原模型兼容
        if training:
            if self.use_hierarchical:
                # 分层分类，返回第二层分类器的输出
                output_list = [outputs[symptom][1] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
            else:
                # 普通分类，直接返回logits
                output_list = [outputs[symptom] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
        else:
            # 推理时，返回最终预测概率
            output_list = [outputs[symptom] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]

        return output_list

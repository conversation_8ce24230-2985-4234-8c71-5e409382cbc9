#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
结合时序增强和跨模态一致性增强的数据处理模块
整合了时序特征提取和处理以及跨模态一致性增强
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import pickle
import csv
import random
from sklearn.model_selection import train_test_split
from scipy.spatial.distance import cosine
from nltk.translate.bleu_score import sentence_bleu

# 设置随机种子以确保结果可复现
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

class CombinedFeatureExtractor:
    """结合时序和跨模态一致性的特征提取器"""
    def __init__(self, data_dir):
        self.data_dir = data_dir

        # 同义词词典（简化版）- 用于文本增强
        self.synonyms = {
            '高兴': ['开心', '愉快', '欢喜', '快乐'],
            '悲伤': ['忧伤', '伤心', '难过', '痛苦'],
            '焦虑': ['紧张', '担忧', '不安', '忧虑'],
            '疲惫': ['疲劳', '困倦', '乏力', '精疲力尽'],
            '愤怒': ['生气', '恼怒', '暴怒', '气愤'],
            '害怕': ['恐惧', '惊恐', '畏惧', '胆怯'],
            '困惑': ['迷惑', '不解', '茫然', '疑惑']
        }

    def extract_features(self, sample_id, question_num):
        """
        提取时序特征和原始特征

        参数:
        - sample_id: 样本ID
        - question_num: 问题编号

        返回:
        - 包含各种特征的字典
        """
        features = {}

        # 基础路径
        sample_dir = os.path.join(self.data_dir, f"{sample_id}_Q{question_num}")

        # 1. 提取视觉时序特征 (OpenFace)
        openface_path = os.path.join(sample_dir, f"Q{question_num}.csv")
        if os.path.exists(openface_path):
            try:
                df = pd.read_csv(openface_path)

                # 提取面部动作单元(AU)的动态变化
                au_intensity_columns = [col for col in df.columns if 'AU' in col and '_r' in col]  # AU强度
                pose_columns = [col for col in df.columns if 'pose' in col]
                gaze_columns = [col for col in df.columns if 'gaze' in col]

                # 组合所有特征
                visual_columns = au_intensity_columns + pose_columns + gaze_columns

                if visual_columns:
                    # 获取时序数据
                    visual_temporal = df[visual_columns].values

                    # 如果序列太长，进行下采样
                    max_seq_len = 100
                    if visual_temporal.shape[0] > max_seq_len:
                        indices = np.linspace(0, visual_temporal.shape[0]-1, max_seq_len, dtype=int)
                        visual_temporal = visual_temporal[indices]

                    features['visual_temporal'] = visual_temporal

                    # 同时保存原始OpenFace特征用于跨模态一致性增强
                    # 计算每列的平均值作为静态特征
                    features['openface'] = np.mean(df[visual_columns].values, axis=0)
            except Exception as e:
                print(f"处理OpenFace特征出错 {sample_dir}: {e}")

        # 2. 提取视觉深度特征 (Q.npy)
        visual_deep_path = os.path.join(sample_dir, f"Q{question_num}.npy")
        if os.path.exists(visual_deep_path):
            try:
                visual_deep = np.load(visual_deep_path)
                # 处理特征形状，确保一致性
                if len(visual_deep.shape) > 1:
                    visual_deep = visual_deep.flatten()
                features['visual_deep'] = visual_deep
            except Exception as e:
                print(f"加载视觉深度特征出错 {sample_dir}：{e}")

        # 3. 提取音频时序特征 (eGeMAPS)
        audio_path = os.path.join(sample_dir, f"Q{question_num}.wav.csv")
        if os.path.exists(audio_path):
            try:
                # 读取CSV文件
                audio_data = []
                with open(audio_path, 'r') as f:
                    for line in f:
                        try:
                            values = [float(x) for x in line.strip().split(',') if x.strip()]
                            audio_data.append(values)
                        except:
                            continue

                if audio_data:
                    audio_temporal = np.array(audio_data)

                    # 如果序列太长，进行下采样
                    max_seq_len = 100
                    if audio_temporal.shape[0] > max_seq_len:
                        indices = np.linspace(0, audio_temporal.shape[0]-1, max_seq_len, dtype=int)
                        audio_temporal = audio_temporal[indices]

                    features['audio_temporal'] = audio_temporal

                    # 同时保存原始音频特征用于跨模态一致性增强
                    # 将所有行合并为一个特征向量
                    audio_flat = np.concatenate([row for row in audio_data])
                    if len(audio_flat) > 128:
                        audio_flat = audio_flat[:128]
                    elif len(audio_flat) < 128:
                        audio_flat = np.pad(audio_flat, (0, 128 - len(audio_flat)), 'constant')
                    features['audio_egemaps'] = audio_flat
            except Exception as e:
                print(f"处理音频特征出错 {sample_dir}: {e}")

        # 4. 提取音频深度特征 (Q.pkl)
        audio_deep_path = os.path.join(sample_dir, f"Q{question_num}.pkl")
        if os.path.exists(audio_deep_path):
            try:
                with open(audio_deep_path, 'rb') as f:
                    audio_deep = pickle.load(f)
                    if isinstance(audio_deep, np.ndarray):
                        if len(audio_deep.shape) > 1:
                            audio_deep = audio_deep.flatten()
                        features['audio_deep'] = audio_deep
            except Exception as e:
                print(f"加载音频深度特征出错 {sample_dir}：{e}")

        # 5. 提取文本特征 (BERT嵌入)
        bert_path = os.path.join(sample_dir, f"vector_Q{question_num}.csv")
        if os.path.exists(bert_path):
            try:
                # 检查文件是否为空
                if os.path.getsize(bert_path) > 0:
                    with open(bert_path, 'r') as f:
                        csv_reader = csv.reader(f)
                        bert_embedding = next(csv_reader)  # 读取第一行
                        if bert_embedding and len(bert_embedding) > 0:
                            # 将字符串转换为浮点数
                            bert_embedding = [float(val) for val in bert_embedding]
                            features['text_embedding'] = np.array(bert_embedding)
            except Exception as e:
                print(f"处理BERT嵌入特征出错 {sample_dir}: {e}")

        # 6. 提取原始文本 (Q.txt)
        text_path = os.path.join(sample_dir, f"Q{question_num}.txt")
        if os.path.exists(text_path):
            try:
                with open(text_path, 'r', encoding='utf-8') as f:
                    raw_text = f.read().strip()
                    if raw_text:
                        features['raw_text'] = raw_text
            except Exception as e:
                print(f"加载原始文本出错 {sample_dir}：{e}")

        return features

    def augment_features(self, features):
        """
        结合时序增强和跨模态一致性增强

        参数:
        - features: 原始特征字典

        返回:
        - 增强后的特征字典
        """
        # 复制原始特征
        augmented_features = {}
        for key, value in features.items():
            if isinstance(value, np.ndarray):
                augmented_features[key] = value.copy()
            elif isinstance(value, torch.Tensor):
                augmented_features[key] = value.clone()
            else:
                augmented_features[key] = value

        # 1. 时序特征增强
        if 'visual_temporal' in features:
            augmented_features['visual_temporal'] = self._augment_temporal_feature(features['visual_temporal'])

        if 'audio_temporal' in features:
            augmented_features['audio_temporal'] = self._augment_temporal_feature(features['audio_temporal'])

        # 2. 跨模态一致性增强
        # 2.1 视觉特征增强
        if 'visual_deep' in features and random.random() < 0.2:
            augmented_features['visual_deep'] = self._augment_visual_feature(features['visual_deep'])

        if 'openface' in features and random.random() < 0.2:
            augmented_features['openface'] = self._augment_openface_feature(features['openface'])

        # 2.2 音频特征增强
        if 'audio_egemaps' in features and random.random() < 0.2:
            augmented_features['audio_egemaps'] = self._augment_audio_feature(features['audio_egemaps'])

        if 'audio_deep' in features and random.random() < 0.2:
            augmented_features['audio_deep'] = self._augment_audio_deep_feature(features['audio_deep'])

        # 2.3 文本特征增强
        if 'text_embedding' in features and random.random() < 0.2:
            augmented_features['text_embedding'] = self._augment_text_feature(features['text_embedding'])

        if 'raw_text' in features and features['raw_text'] and random.random() < 0.2:
            augmented_features['raw_text'] = self._augment_text(features['raw_text'])

            # 如果文本被增强，更新文本嵌入
            if 'text_embedding' in features and augmented_features['raw_text'] != features['raw_text']:
                # 这里简化处理，实际应用中应该重新计算BERT嵌入
                # 这里我们只是添加一些小噪声来模拟嵌入变化
                noise = np.random.randn(*features['text_embedding'].shape) * 0.01
                augmented_features['text_embedding'] = features['text_embedding'] + noise

        # 3. 确保跨模态一致性
        # 检查增强后的特征是否保持一致性，如果不一致，回退到原始特征
        self._ensure_cross_modal_consistency(features, augmented_features)

        return augmented_features

    def _augment_temporal_feature(self, feature):
        """时序特征增强"""
        # 复制原始特征
        augmented_feature = feature.copy()

        # 添加小幅度噪声
        noise_scale = 0.02  # 2%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise

        # 随机时间掩码 - 掩盖一些时间步
        mask_ratio = 0.05  # 5%的时间步被掩码
        time_steps = augmented_feature.shape[0]
        mask_indices = np.random.choice(time_steps, int(time_steps * mask_ratio), replace=False)
        augmented_feature[mask_indices] = 0

        # 随机特征掩码 - 掩盖一些特征维度
        mask_ratio = 0.05  # 5%的特征被掩码
        feature_dims = augmented_feature.shape[1]
        mask_indices = np.random.choice(feature_dims, int(feature_dims * mask_ratio), replace=False)
        augmented_feature[:, mask_indices] = 0

        return augmented_feature

    def _augment_visual_feature(self, visual_feature):
        """视频特征增强：局部偏移、轻微仿射变换、局部遮挡"""
        # 复制原始特征
        augmented_feature = visual_feature.copy()

        # 模拟轻微仿射变换（通过小幅度缩放特征）
        transform_factor = random.uniform(0.95, 1.05)  # ±5%缩放
        augmented_feature = augmented_feature * transform_factor

        # 模拟局部遮挡（随机将小部分特征置零）
        mask_size = int(len(augmented_feature) * 0.05)  # 5%的特征被遮挡
        if mask_size > 0:
            mask_start = random.randint(0, len(augmented_feature) - mask_size - 1)
            augmented_feature[mask_start:mask_start+mask_size] = 0

        # 一致性检测 - 余弦相似度
        similarity = 1 - cosine(visual_feature, augmented_feature)

        # 如果相似度太低，则返回原始特征
        if similarity < 0.9:
            return visual_feature

        return augmented_feature

    def _augment_openface_feature(self, openface_feature):
        """OpenFace特征增强：特征扰动、随机掩码"""
        # 复制原始特征
        augmented_feature = openface_feature.copy()

        # 特征扰动（小幅度随机噪声）
        noise_scale = 0.01  # 1%的噪声，OpenFace特征对噪声更敏感
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise

        # 随机特征掩码（随机将少量特征置零）
        mask_ratio = 0.03  # 3%的特征被掩码
        mask_size = int(len(augmented_feature) * mask_ratio)
        if mask_size > 0:
            mask_indices = np.random.choice(len(augmented_feature), mask_size, replace=False)
            augmented_feature[mask_indices] = 0

        # 一致性检测 - 余弦相似度
        similarity = 1 - cosine(openface_feature, augmented_feature)

        # 如果相似度太低，则返回原始特征
        if similarity < 0.95:  # OpenFace特征需要更高的一致性
            return openface_feature

        return augmented_feature

    def _augment_audio_feature(self, audio_feature):
        """音频特征增强：语速调整、音量调整、噪声注入"""
        # 复制原始特征
        augmented_feature = audio_feature.copy()

        # 模拟语速调整（通过小幅度缩放特征）
        speed_factor = random.uniform(0.9, 1.1)
        augmented_feature = augmented_feature * speed_factor

        # 模拟音量调整（通过小幅度缩放特征）
        volume_factor = random.uniform(0.95, 1.05)  # ±5dB
        augmented_feature = augmented_feature * volume_factor

        # 小幅噪声注入 (SNR > 30dB)
        # SNR = 10*log10(signal_power/noise_power)
        # 对于SNR=30dB, noise_power = signal_power / 1000
        signal_power = np.mean(augmented_feature ** 2)
        noise_power = signal_power / 1000
        noise = np.random.randn(*augmented_feature.shape) * np.sqrt(noise_power)
        augmented_feature = augmented_feature + noise

        # 一致性检测 - 余弦相似度
        similarity = 1 - cosine(audio_feature, augmented_feature)

        # 如果相似度太低，则返回原始特征
        if similarity < 0.8:
            return audio_feature

        return augmented_feature

    def _augment_audio_deep_feature(self, audio_deep_feature):
        """音频深度特征增强：特征扰动、随机掩码"""
        # 复制原始特征
        augmented_feature = audio_deep_feature.copy()

        # 特征扰动（小幅度随机噪声）
        noise_scale = 0.02  # 2%的噪声
        noise = np.random.randn(*augmented_feature.shape) * noise_scale
        augmented_feature = augmented_feature + noise

        # 随机特征掩码（随机将少量特征置零）
        mask_ratio = 0.05  # 5%的特征被掩码
        mask_size = int(len(augmented_feature) * mask_ratio)
        if mask_size > 0:
            mask_indices = np.random.choice(len(augmented_feature), mask_size, replace=False)
            augmented_feature[mask_indices] = 0

        # 特征缩放（模拟音频增益变化）
        scale_factor = random.uniform(0.95, 1.05)
        augmented_feature = augmented_feature * scale_factor

        # 一致性检测 - 余弦相似度
        similarity = 1 - cosine(audio_deep_feature, augmented_feature)

        # 如果相似度太低，则返回原始特征
        if similarity < 0.85:
            return audio_deep_feature

        return augmented_feature

    def _augment_text(self, text):
        """文本增强：同义词替换、小幅句式重构"""
        if not text:
            return text

        words = list(text)  # 对中文文本，每个字符作为一个单位

        # 确定要替换的词数量（替换比例控制在10%-20%之内）
        num_to_replace = max(1, int(len(words) * random.uniform(0.1, 0.2)))

        # 执行同义词替换
        for _ in range(num_to_replace):
            idx = random.randint(0, len(words) - 1)
            word = words[idx]

            # 检查是否有同义词
            if word in self.synonyms and self.synonyms[word]:
                words[idx] = random.choice(self.synonyms[word])

        # 小幅句式重构（概率50%）
        augmented_text = ''.join(words)
        if random.random() < 0.5:
            # 陈述句转疑问句
            if not augmented_text.endswith('？') and not augmented_text.endswith('?'):
                augmented_text = augmented_text.rstrip('。.!！') + '？'

        # 一致性检测 - 使用BLEU得分
        try:
            reference = [list(text)]  # 原始文本作为参考
            candidate = list(augmented_text)  # 增强后的文本作为候选

            bleu_score = sentence_bleu(reference, candidate)

            # 如果BLEU得分太低，则返回原始文本
            if bleu_score < 0.7:
                return text
        except:
            # 如果BLEU计算失败，返回原始文本
            return text

        return augmented_text

    def _augment_text_feature(self, text_feature):
        """文本特征向量增强"""
        # 复制原始特征
        augmented_feature = text_feature.copy()

        # 小幅度噪声注入 (±1%)
        noise = np.random.randn(*augmented_feature.shape) * 0.01
        augmented_feature = augmented_feature + noise

        # 随机特征掩码（掩码比例5%）
        mask_ratio = 0.05
        mask_size = int(len(augmented_feature) * mask_ratio)
        if mask_size > 0:
            mask_indices = np.random.choice(len(augmented_feature), mask_size, replace=False)
            augmented_feature[mask_indices] = 0

        # 一致性检测 - 余弦相似度
        similarity = 1 - cosine(text_feature, augmented_feature)

        # 如果相似度太低，则返回原始特征
        if similarity < 0.7:
            return text_feature

        return augmented_feature

    def _ensure_cross_modal_consistency(self, original_features, augmented_features):
        """确保跨模态一致性"""
        # 由于不同模态的特征维度可能不同，我们不直接计算它们之间的相似度
        # 而是比较增强前后每个模态自身的变化

        # 检查视觉特征的一致性
        if 'visual_deep' in original_features and 'visual_deep' in augmented_features:
            # 计算原始特征和增强特征之间的相似度
            visual_sim = 1 - cosine(original_features['visual_deep'], augmented_features['visual_deep'])

            # 如果相似度太低，回退到原始特征
            if visual_sim < 0.8:
                augmented_features['visual_deep'] = original_features['visual_deep'].copy()

        # 检查音频特征的一致性
        if 'audio_deep' in original_features and 'audio_deep' in augmented_features:
            # 计算原始特征和增强特征之间的相似度
            audio_sim = 1 - cosine(original_features['audio_deep'], augmented_features['audio_deep'])

            # 如果相似度太低，回退到原始特征
            if audio_sim < 0.8:
                augmented_features['audio_deep'] = original_features['audio_deep'].copy()

        # 检查文本特征的一致性
        if 'text_embedding' in original_features and 'text_embedding' in augmented_features:
            # 计算原始特征和增强特征之间的相似度
            text_sim = 1 - cosine(original_features['text_embedding'], augmented_features['text_embedding'])

            # 如果相似度太低，回退到原始特征
            if text_sim < 0.8:
                augmented_features['text_embedding'] = original_features['text_embedding'].copy()


class CombinedTemporalConsistencyDataset(Dataset):
    """结合时序增强和跨模态一致性增强的数据集"""
    def __init__(self, data_dir, samples, symptom_types, max_seq_len=100, augment_prob=0.2, use_temporal=True, use_cross_modal=True):
        """
        初始化数据集

        参数:
        - data_dir: 数据目录
        - samples: 样本列表 [(sample_id, question_num, labels), ...]
        - symptom_types: 症状类型列表
        - max_seq_len: 最大序列长度
        - augment_prob: 数据增强概率
        - use_temporal: 是否使用时序增强
        - use_cross_modal: 是否使用跨模态一致性增强
        """
        self.data_dir = data_dir
        self.samples = samples
        self.symptom_types = symptom_types
        self.max_seq_len = max_seq_len
        self.augment_prob = augment_prob
        self.use_temporal = use_temporal
        self.use_cross_modal = use_cross_modal

        # 特征提取器
        self.feature_extractor = CombinedFeatureExtractor(data_dir)

        # 缓存
        self.cache = {}

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        # 检查缓存
        if idx in self.cache:
            return self.cache[idx]

        sample_id, question_num, labels = self.samples[idx]

        # 提取特征
        features = self.feature_extractor.extract_features(sample_id, question_num)

        # 根据配置应用数据增强
        if np.random.random() < self.augment_prob:
            # 根据配置决定是否使用时序增强和跨模态一致性增强
            if self.use_temporal and self.use_cross_modal:
                # 使用完整的增强
                features = self.feature_extractor.augment_features(features)
            elif self.use_temporal:
                # 只使用时序增强
                if 'visual_temporal' in features:
                    features['visual_temporal'] = self.feature_extractor._augment_temporal_feature(features['visual_temporal'])
                if 'audio_temporal' in features:
                    features['audio_temporal'] = self.feature_extractor._augment_temporal_feature(features['audio_temporal'])
            elif self.use_cross_modal:
                # 只使用跨模态一致性增强
                augmented_features = {}
                for key, value in features.items():
                    if isinstance(value, np.ndarray):
                        augmented_features[key] = value.copy()
                    elif isinstance(value, torch.Tensor):
                        augmented_features[key] = value.clone()
                    else:
                        augmented_features[key] = value

                # 应用跨模态一致性增强，但不增强时序特征
                if 'visual_deep' in features and random.random() < 0.2:
                    augmented_features['visual_deep'] = self.feature_extractor._augment_visual_feature(features['visual_deep'])
                if 'audio_deep' in features and random.random() < 0.2:
                    augmented_features['audio_deep'] = self.feature_extractor._augment_audio_deep_feature(features['audio_deep'])
                if 'text_embedding' in features and random.random() < 0.2:
                    augmented_features['text_embedding'] = self.feature_extractor._augment_text_feature(features['text_embedding'])

                # 确保跨模态一致性
                self.feature_extractor._ensure_cross_modal_consistency(features, augmented_features)
                features = augmented_features

        # 处理视觉时序特征
        if 'visual_temporal' in features:
            visual_temporal = features['visual_temporal']

            # 确保序列长度一致
            if visual_temporal.shape[0] < self.max_seq_len:
                # 填充序列
                padding = np.zeros((self.max_seq_len - visual_temporal.shape[0], visual_temporal.shape[1]))
                visual_temporal = np.vstack([visual_temporal, padding])
            elif visual_temporal.shape[0] > self.max_seq_len:
                # 截断序列
                visual_temporal = visual_temporal[:self.max_seq_len]

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 128
            if visual_temporal.shape[1] > fixed_dim:
                # 如果特征维度太大，截断
                visual_temporal = visual_temporal[:, :fixed_dim]
            elif visual_temporal.shape[1] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros((visual_temporal.shape[0], fixed_dim - visual_temporal.shape[1]))
                visual_temporal = np.hstack([visual_temporal, padding])
        else:
            # 如果没有视觉特征，使用零向量
            visual_temporal = np.zeros((self.max_seq_len, 128))  # 固定视觉特征维度为128

        # 处理音频时序特征
        if 'audio_temporal' in features:
            audio_temporal = features['audio_temporal']

            # 确保序列长度一致
            if audio_temporal.shape[0] < self.max_seq_len:
                # 填充序列
                padding = np.zeros((self.max_seq_len - audio_temporal.shape[0], audio_temporal.shape[1]))
                audio_temporal = np.vstack([audio_temporal, padding])
            elif audio_temporal.shape[0] > self.max_seq_len:
                # 截断序列
                audio_temporal = audio_temporal[:self.max_seq_len]

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 128
            if audio_temporal.shape[1] > fixed_dim:
                # 如果特征维度太大，截断
                audio_temporal = audio_temporal[:, :fixed_dim]
            elif audio_temporal.shape[1] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros((audio_temporal.shape[0], fixed_dim - audio_temporal.shape[1]))
                audio_temporal = np.hstack([audio_temporal, padding])
        else:
            # 如果没有音频特征，使用零向量
            audio_temporal = np.zeros((self.max_seq_len, 128))  # 固定音频特征维度为128

        # 处理文本特征
        if 'text_embedding' in features:
            text_embedding = features['text_embedding']

            # 确保特征维度一致 - 使用固定维度
            fixed_dim = 768  # BERT嵌入维度
            if text_embedding.shape[0] > fixed_dim:
                # 如果特征维度太大，截断
                text_embedding = text_embedding[:fixed_dim]
            elif text_embedding.shape[0] < fixed_dim:
                # 如果特征维度太小，填充
                padding = np.zeros(fixed_dim - text_embedding.shape[0])
                text_embedding = np.concatenate([text_embedding, padding])

            # 文本特征通常是静态的，我们可以将其复制为序列
            text_temporal = np.tile(text_embedding, (self.max_seq_len, 1))
        else:
            # 如果没有文本特征，使用零向量
            text_temporal = np.zeros((self.max_seq_len, 768))  # BERT嵌入维度为768

        # 转换为张量
        visual_tensor = torch.tensor(visual_temporal, dtype=torch.float32)
        audio_tensor = torch.tensor(audio_temporal, dtype=torch.float32)
        text_tensor = torch.tensor(text_temporal, dtype=torch.float32)

        # 转换标签为张量
        label_tensors = [torch.tensor(label, dtype=torch.long) for label in labels]

        # 缓存结果
        result = (visual_tensor, audio_tensor, text_tensor, label_tensors)
        self.cache[idx] = result

        return result


def combined_collate_fn(batch):
    """
    自定义的collate函数，用于处理批次数据

    参数:
    - batch: 批次数据

    返回:
    - 处理后的批次数据
    """
    return (
        torch.stack([item[0] for item in batch]),
        torch.stack([item[1] for item in batch]),
        torch.stack([item[2] for item in batch]),
        [item[3] for item in batch]
    )

def create_combined_dataloader(dataset, batch_size, shuffle=True):
    """
    创建数据加载器

    参数:
    - dataset: 数据集
    - batch_size: 批次大小
    - shuffle: 是否打乱数据

    返回:
    - 数据加载器
    """
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=0,  # 设置为0以避免多进程问题
        pin_memory=True,
        collate_fn=combined_collate_fn
    )


def prepare_combined_data(data_dir, label_file, max_seq_len=100, batch_size=32, augment_prob=0.2, test_size=0.2, val_size=0.1, random_state=42, use_temporal=True, use_cross_modal=True):
    """
    准备数据集

    参数:
    - data_dir: 数据目录
    - label_file: 标签文件路径
    - max_seq_len: 最大序列长度
    - batch_size: 批次大小
    - augment_prob: 数据增强概率
    - test_size: 测试集比例
    - val_size: 验证集比例
    - random_state: 随机种子
    - use_temporal: 是否使用时序增强
    - use_cross_modal: 是否使用跨模态一致性增强

    返回:
    - 训练、验证和测试数据加载器
    - 症状类型列表
    """
    # 设置随机种子
    set_seed(random_state)

    # 读取标签文件
    try:
        label_df = pd.read_excel(label_file)
    except:
        print(f"无法读取标签文件: {label_file}")
        return None, None, None, None

    # 提取症状类型
    symptom_columns = ['情绪症状', '躯体症状', '认知症状', '行为症状']
    symptom_types = ['emotional', 'somatic', 'cognitive', 'behavioral']

    # 准备样本列表
    samples = []
    for _, row in label_df.iterrows():
        sample_id_with_q = row['SampleID']

        # 从SampleID中提取sample_id和question_num
        # 格式为：HC03_Q1，其中HC03是sample_id，1是question_num
        parts = sample_id_with_q.split('_Q')
        if len(parts) != 2:
            print(f"警告：无法解析SampleID: {sample_id_with_q}")
            continue

        sample_id = parts[0]
        question_num = parts[1]

        # 提取标签，将NaN值替换为0
        labels = [int(row[col]) if not pd.isna(row[col]) else 0 for col in symptom_columns]

        samples.append((sample_id, question_num, labels))

    # 划分训练集、验证集和测试集
    train_val_samples, test_samples = train_test_split(samples, test_size=test_size, random_state=random_state)
    train_samples, val_samples = train_test_split(train_val_samples, test_size=val_size/(1-test_size), random_state=random_state)

    print(f"训练集样本数: {len(train_samples)}")
    print(f"验证集样本数: {len(val_samples)}")
    print(f"测试集样本数: {len(test_samples)}")

    # 创建数据集
    train_dataset = CombinedTemporalConsistencyDataset(
        data_dir=data_dir,
        samples=train_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        augment_prob=augment_prob,
        use_temporal=use_temporal,
        use_cross_modal=use_cross_modal
    )

    val_dataset = CombinedTemporalConsistencyDataset(
        data_dir=data_dir,
        samples=val_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        augment_prob=0.0,  # 验证集不使用数据增强
        use_temporal=use_temporal,
        use_cross_modal=use_cross_modal
    )

    test_dataset = CombinedTemporalConsistencyDataset(
        data_dir=data_dir,
        samples=test_samples,
        symptom_types=symptom_types,
        max_seq_len=max_seq_len,
        augment_prob=0.0,  # 测试集不使用数据增强
        use_temporal=use_temporal,
        use_cross_modal=use_cross_modal
    )

    # 创建数据加载器
    train_loader = create_combined_dataloader(train_dataset, batch_size, shuffle=True)
    val_loader = create_combined_dataloader(val_dataset, batch_size, shuffle=False)
    test_loader = create_combined_dataloader(test_dataset, batch_size, shuffle=False)

    return train_loader, val_loader, test_loader, symptom_types
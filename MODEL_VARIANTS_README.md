# 模型变体实验

本项目实现了多种模型架构，用于多模态抑郁症状识别任务。

## 模型架构

1. **Self-attention**: 使用自注意力机制处理单一模态内的特征关系。
2. **Cross-attention**: 使用跨模态注意力机制处理不同模态之间的特征交互。
3. **CubeMLP**: 使用立方体MLP结构处理多维特征。
4. **GRU-BiLSTM**: 结合GRU和BiLSTM的优势，先使用GRU处理序列，再使用BiLSTM进一步提取特征。
5. **Bi-LSTM**: 使用双向LSTM捕捉序列的前向和后向依赖关系。
6. **MulT (Multimodal Transformer)**: 多模态Transformer模型，通过跨模态注意力机制融合不同模态的信息。
7. **PMR (Progressive Multimodal Reasoning)**: 渐进式多模态推理模型，通过多步推理过程逐步融合和推理多模态信息。

## 文件结构

- `models/`: 包含所有模型架构的实现
  - `self_attention_model.py`: Self-attention模型实现
  - `cross_attention_model.py`: Cross-attention模型实现
  - `cube_mlp_model.py`: CubeMLP模型实现
  - `gru_bilstm_model.py`: GRU-BiLSTM模型实现
  - `bilstm_model.py`: Bi-LSTM模型实现
  - `mult_model.py`: MulT (Multimodal Transformer)模型实现
  - `pmr_model.py`: PMR (Progressive Multimodal Reasoning)模型实现
- `train_model_variants.py`: 训练和评估模型的脚本
- `compare_model_variants.py`: 比较不同模型变体结果的脚本
- `run_model_variants.sh`: 运行所有模型变体实验的Shell脚本

## 使用方法

### 训练单个模型

```bash
python train_model_variants.py --model_type [model_type] --epochs 50 --batch_size 16 --learning_rate 0.0005 --use_focal_loss
```

其中 `[model_type]` 可以是以下之一：
- `self_attention`
- `cross_attention`
- `cube_mlp`
- `gru_bilstm`
- `bilstm`
- `mult`
- `pmr`
- `all` (训练所有模型)

### 运行所有模型实验

```bash
bash run_model_variants.sh
```

### 比较模型结果

```bash
python compare_model_variants.py
```

如果要更新综合比较报告，可以添加 `--update_comprehensive` 参数：

```bash
python compare_model_variants.py --update_comprehensive
```

## 参数说明

### 训练参数

- `--data_dir`: 数据目录，默认为 `/Users/<USER>/Downloads/CMDC_ATV/shuju`
- `--label_file`: 标签文件路径，默认为 `/Users/<USER>/Downloads/CMDC_ATV/symptom_label_template.xlsx`
- `--max_seq_len`: 最大序列长度，默认为 100
- `--batch_size`: 批次大小，默认为 16
- `--epochs`: 训练轮数，默认为 50
- `--learning_rate`: 学习率，默认为 0.0005
- `--weight_decay`: 权重衰减，默认为 1e-5
- `--patience`: 早停耐心值，默认为 10
- `--min_delta`: 早停最小改进阈值，默认为 0.001
- `--use_focal_loss`: 是否使用Focal Loss，默认为 False
- `--focal_loss_gamma`: Focal Loss的gamma参数，默认为 2.0

### 模型参数

- `--hidden_dim`: 隐藏层维度，默认为 256
- `--num_heads`: 注意力头数量，默认为 4
- `--num_layers`: 层数，默认为 2
- `--dropout`: Dropout比率，默认为 0.3

### 其他参数

- `--seed`: 随机种子，默认为 42
- `--device`: 训练设备，默认为 'cuda' 如果可用，否则为 'cpu'
- `--augment_prob`: 数据增强概率，默认为 0.2

## 结果

实验结果将保存在 `ablation_results/` 目录下，包括：

- 各模型的测试结果 (`*_results.json`)
- 各模型的训练历史 (`*_history.json`)
- 模型变体比较报告 (`model_variants_comparison.md`)
- 更新后的综合比较报告 (`comprehensive_comparison_report.md`)

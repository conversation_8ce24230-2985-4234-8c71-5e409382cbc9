import os
import csv
import numpy as np
import pandas as pd
import pickle
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectFromModel
from sklearn.ensemble import RandomForestClassifier

def extract_advanced_features(data_dir, sample_id, question_num):
    """
    提取高级特征

    参数:
    - data_dir: 数据目录
    - sample_id: 样本ID
    - question_num: 问题编号

    返回:
    - 特征字典
    """
    features = {}

    # 基础路径
    sample_dir = os.path.join(data_dir, f"{sample_id}_Q{question_num}")

    # 1. 视觉特征增强
    # 1.1 提取OpenFace动态特征
    openface_path = os.path.join(sample_dir, f"Q{question_num}.csv")
    if os.path.exists(openface_path):
        try:
            df = pd.read_csv(openface_path)

            # 提取面部动作单元(AU)的动态变化
            au_intensity_columns = [col for col in df.columns if 'AU' in col and '_r' in col]  # AU强度
            au_presence_columns = [col for col in df.columns if 'AU' in col and '_c' in col]  # AU存在

            # 处理AU强度特征
            if au_intensity_columns:
                au_features = df[au_intensity_columns].values
                # 计算统计特征：均值、标准差、最大值、最小值、范围
                features['au_intensity_mean'] = np.mean(au_features, axis=0)
                features['au_intensity_std'] = np.std(au_features, axis=0)
                features['au_intensity_max'] = np.max(au_features, axis=0)
                features['au_intensity_min'] = np.min(au_features, axis=0)
                features['au_intensity_range'] = features['au_intensity_max'] - features['au_intensity_min']

                # 计算变化率特征（如果有足够的帧）
                if au_features.shape[0] > 1:
                    au_diff = np.diff(au_features, axis=0)
                    features['au_intensity_diff_mean'] = np.mean(au_diff, axis=0)
                    features['au_intensity_diff_std'] = np.std(au_diff, axis=0)

            # 处理AU存在特征
            if au_presence_columns:
                au_presence = df[au_presence_columns].values
                # 计算每个AU的存在比例
                features['au_presence_ratio'] = np.mean(au_presence, axis=0)

            # 提取头部姿态特征
            pose_columns = [col for col in df.columns if 'pose' in col]
            if pose_columns:
                pose_features = df[pose_columns].values
                features['pose_mean'] = np.mean(pose_features, axis=0)
                features['pose_std'] = np.std(pose_features, axis=0)

                # 计算头部运动的总量（如果有足够的帧）
                if pose_features.shape[0] > 1:
                    pose_diff = np.diff(pose_features, axis=0)
                    features['pose_movement'] = np.sum(np.abs(pose_diff), axis=0)

            # 提取眼睛凝视特征
            gaze_columns = [col for col in df.columns if 'gaze' in col]
            if gaze_columns:
                gaze_features = df[gaze_columns].values
                features['gaze_mean'] = np.mean(gaze_features, axis=0)
                features['gaze_std'] = np.std(gaze_features, axis=0)

                # 计算眼睛运动的总量（如果有足够的帧）
                if gaze_features.shape[0] > 1:
                    gaze_diff = np.diff(gaze_features, axis=0)
                    features['gaze_movement'] = np.sum(np.abs(gaze_diff), axis=0)
        except Exception as e:
            print(f"处理OpenFace特征出错 {sample_dir}: {e}")

    # 1.2 提取视觉深度特征并增强
    visual_deep_path = os.path.join(sample_dir, f"Q{question_num}.npy")
    if os.path.exists(visual_deep_path):
        try:
            visual_deep = np.load(visual_deep_path)

            # 确保特征是二维的
            if len(visual_deep.shape) == 1:
                visual_deep = visual_deep.reshape(1, -1)

            # 如果是时序数据，提取时序特征
            if visual_deep.shape[0] > 1:
                # 计算时序统计特征
                features['visual_deep_mean'] = np.mean(visual_deep, axis=0)
                features['visual_deep_std'] = np.std(visual_deep, axis=0)
                features['visual_deep_max'] = np.max(visual_deep, axis=0)
                features['visual_deep_min'] = np.min(visual_deep, axis=0)

                # 计算变化率
                visual_deep_diff = np.diff(visual_deep, axis=0)
                features['visual_deep_diff_mean'] = np.mean(visual_deep_diff, axis=0)
                features['visual_deep_diff_std'] = np.std(visual_deep_diff, axis=0)
            else:
                # 如果只有一帧，直接使用特征
                features['visual_deep'] = visual_deep.flatten()
        except Exception as e:
            print(f"处理视觉深度特征出错 {sample_dir}: {e}")

    # 2. 音频特征增强
    # 2.1 提取eGeMAPS高级特征
    audio_path = os.path.join(sample_dir, f"Q{question_num}.wav.csv")
    if os.path.exists(audio_path):
        try:
            # 读取CSV文件
            audio_data = []
            with open(audio_path, 'r') as f:
                for line in f:
                    try:
                        values = [float(x) for x in line.strip().split(',') if x.strip()]
                        audio_data.append(values)
                    except:
                        continue

            if audio_data:
                audio_data = np.array(audio_data)

                # 如果是单行数据，直接使用
                if len(audio_data.shape) == 1 or audio_data.shape[0] == 1:
                    features['audio_egemaps'] = audio_data.flatten()
                else:
                    # 计算统计特征
                    features['audio_egemaps_mean'] = np.mean(audio_data, axis=0)
                    features['audio_egemaps_std'] = np.std(audio_data, axis=0)
                    features['audio_egemaps_max'] = np.max(audio_data, axis=0)
                    features['audio_egemaps_min'] = np.min(audio_data, axis=0)

                    # 计算变化率（如果有足够的帧）
                    if audio_data.shape[0] > 1:
                        audio_diff = np.diff(audio_data, axis=0)
                        features['audio_egemaps_diff_mean'] = np.mean(audio_diff, axis=0)
                        features['audio_egemaps_diff_std'] = np.std(audio_diff, axis=0)
        except Exception as e:
            print(f"处理eGeMAPS特征出错 {sample_dir}: {e}")

    # 2.2 提取音频深度特征并增强
    audio_deep_path = os.path.join(sample_dir, f"Q{question_num}.pkl")
    if os.path.exists(audio_deep_path):
        try:
            with open(audio_deep_path, 'rb') as f:
                audio_deep = pickle.load(f)

                # 确保特征是numpy数组
                if isinstance(audio_deep, np.ndarray):
                    # 确保特征是二维的
                    if len(audio_deep.shape) == 1:
                        audio_deep = audio_deep.reshape(1, -1)

                    # 如果是时序数据，提取时序特征
                    if audio_deep.shape[0] > 1:
                        # 计算时序统计特征
                        features['audio_deep_mean'] = np.mean(audio_deep, axis=0)
                        features['audio_deep_std'] = np.std(audio_deep, axis=0)
                        features['audio_deep_max'] = np.max(audio_deep, axis=0)
                        features['audio_deep_min'] = np.min(audio_deep, axis=0)

                        # 计算变化率
                        audio_deep_diff = np.diff(audio_deep, axis=0)
                        features['audio_deep_diff_mean'] = np.mean(audio_deep_diff, axis=0)
                        features['audio_deep_diff_std'] = np.std(audio_deep_diff, axis=0)
                    else:
                        # 如果只有一帧，直接使用特征
                        features['audio_deep'] = audio_deep.flatten()
        except Exception as e:
            print(f"处理音频深度特征出错 {sample_dir}: {e}")

    # 3. 文本特征增强
    # 3.1 提取原始文本特征
    text_path = os.path.join(sample_dir, f"Q{question_num}.txt")
    if os.path.exists(text_path):
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                text = f.read().strip()

                # 提取文本统计特征
                if text:
                    # 文本长度
                    features['text_length'] = np.array([len(text)])

                    # 词数
                    words = text.split()
                    features['word_count'] = np.array([len(words)])

                    # 平均词长
                    if words:
                        features['avg_word_length'] = np.array([np.mean([len(word) for word in words])])

                    # 标点符号数量
                    punctuation_count = sum(1 for char in text if char in ',.!?;:')
                    features['punctuation_count'] = np.array([punctuation_count])

                    # 情感词汇比例
                    emotion_words = ['高兴', '开心', '愉快', '悲伤', '难过', '痛苦', '焦虑', '担忧', '害怕', '恐惧', '愤怒', '生气']
                    emotion_word_count = sum(1 for word in words if word in emotion_words)
                    if words:
                        features['emotion_word_ratio'] = np.array([emotion_word_count / len(words)])

                    # 躯体症状词汇比例
                    physical_words = ['疲劳', '疼痛', '头痛', '头晕', '乏力', '无力', '困倦', '睡眠', '食欲']
                    physical_word_count = sum(1 for word in words if word in physical_words)
                    if words:
                        features['physical_word_ratio'] = np.array([physical_word_count / len(words)])

                    # 认知症状词汇比例
                    cognitive_words = ['记忆', '注意', '思考', '集中', '忘记', '混乱', '困惑', '理解', '决定']
                    cognitive_word_count = sum(1 for word in words if word in cognitive_words)
                    if words:
                        features['cognitive_word_ratio'] = np.array([cognitive_word_count / len(words)])

                    # 行为症状词汇比例
                    behavioral_words = ['行为', '活动', '退缩', '激动', '冲动', '重复', '回避', '逃避']
                    behavioral_word_count = sum(1 for word in words if word in behavioral_words)
                    if words:
                        features['behavioral_word_ratio'] = np.array([behavioral_word_count / len(words)])
        except Exception as e:
            print(f"处理原始文本特征出错 {sample_dir}: {e}")

    # 3.2 提取BERT嵌入特征
    bert_path = os.path.join(sample_dir, f"vector_Q{question_num}.csv")
    if os.path.exists(bert_path):
        try:
            # 检查文件是否为空
            if os.path.getsize(bert_path) > 0:
                with open(bert_path, 'r') as f:
                    csv_reader = csv.reader(f)
                    bert_embedding = next(csv_reader)  # 读取第一行
                    if bert_embedding and len(bert_embedding) > 0:
                        # 将字符串转换为浮点数
                        bert_embedding = [float(val) for val in bert_embedding]
                        features['bert_embedding'] = np.array(bert_embedding)
        except Exception as e:
            print(f"处理BERT嵌入特征出错 {sample_dir}: {e}")

    return features

def combine_features(features_dict):
    """
    组合所有特征为一个向量

    参数:
    - features_dict: 特征字典

    返回:
    - 组合后的特征向量
    """
    # 收集所有特征
    all_features = []

    # 处理每个特征
    for key, value in features_dict.items():
        # 确保特征是一维的
        if isinstance(value, np.ndarray):
            if len(value.shape) > 1:
                value = value.flatten()
            all_features.append(value)

    # 如果没有特征，返回空数组
    if not all_features:
        return np.array([])

    # 组合所有特征
    return np.concatenate(all_features)

def reduce_feature_dimension(features, max_dim=128):
    """
    降低特征维度

    参数:
    - features: 特征向量
    - max_dim: 最大维度

    返回:
    - 降维后的特征
    """
    # 如果特征维度小于等于最大维度，直接返回
    if features.shape[0] <= max_dim:
        return features

    # 简单截断，避免PCA的问题
    return features[:max_dim]

def create_feature_interactions(visual_feature, audio_feature, text_feature):
    """
    创建特征交互项

    参数:
    - visual_feature: 视觉特征
    - audio_feature: 音频特征
    - text_feature: 文本特征

    返回:
    - 交互特征
    """
    interactions = []

    # 确保所有特征都是一维的
    if len(visual_feature.shape) > 1:
        visual_feature = visual_feature.flatten()
    if len(audio_feature.shape) > 1:
        audio_feature = audio_feature.flatten()
    if len(text_feature.shape) > 1:
        text_feature = text_feature.flatten()

    # 选择每个模态的前10个特征（避免维度爆炸）
    v_feat = visual_feature[:10] if visual_feature.shape[0] >= 10 else visual_feature
    a_feat = audio_feature[:10] if audio_feature.shape[0] >= 10 else audio_feature
    t_feat = text_feature[:10] if text_feature.shape[0] >= 10 else text_feature

    # 创建视觉-音频交互
    for i in range(len(v_feat)):
        for j in range(len(a_feat)):
            interactions.append(v_feat[i] * a_feat[j])

    # 创建视觉-文本交互
    for i in range(len(v_feat)):
        for j in range(len(t_feat)):
            interactions.append(v_feat[i] * t_feat[j])

    # 创建音频-文本交互
    for i in range(len(a_feat)):
        for j in range(len(t_feat)):
            interactions.append(a_feat[i] * t_feat[j])

    # 如果没有交互特征，返回空数组
    if not interactions:
        return np.array([])

    return np.array(interactions)

class AdvancedFeatureExtractor:
    """高级特征提取器"""

    def __init__(self, data_dir):
        """
        初始化特征提取器

        参数:
        - data_dir: 数据目录
        """
        self.data_dir = data_dir

    def extract_features(self, sample_id, question_num):
        """
        提取特征

        参数:
        - sample_id: 样本ID
        - question_num: 问题编号

        返回:
        - 视觉特征
        - 音频特征
        - 文本特征
        """
        # 提取高级特征
        features_dict = extract_advanced_features(self.data_dir, sample_id, question_num)

        # 分类特征
        visual_features = []
        audio_features = []
        text_features = []

        # 收集视觉特征
        visual_keys = ['au_intensity_mean', 'au_intensity_std', 'au_intensity_max', 'au_intensity_min',
                      'au_intensity_range', 'au_intensity_diff_mean', 'au_intensity_diff_std',
                      'au_presence_ratio', 'pose_mean', 'pose_std', 'pose_movement',
                      'gaze_mean', 'gaze_std', 'gaze_movement',
                      'visual_deep', 'visual_deep_mean', 'visual_deep_std', 'visual_deep_max',
                      'visual_deep_min', 'visual_deep_diff_mean', 'visual_deep_diff_std']

        for key in visual_keys:
            if key in features_dict:
                visual_features.append(features_dict[key])

        # 收集音频特征
        audio_keys = ['audio_egemaps', 'audio_egemaps_mean', 'audio_egemaps_std', 'audio_egemaps_max',
                     'audio_egemaps_min', 'audio_egemaps_diff_mean', 'audio_egemaps_diff_std',
                     'audio_deep', 'audio_deep_mean', 'audio_deep_std', 'audio_deep_max',
                     'audio_deep_min', 'audio_deep_diff_mean', 'audio_deep_diff_std']

        for key in audio_keys:
            if key in features_dict:
                audio_features.append(features_dict[key])

        # 收集文本特征
        text_keys = ['text_length', 'word_count', 'avg_word_length', 'punctuation_count',
                    'emotion_word_ratio', 'physical_word_ratio', 'cognitive_word_ratio',
                    'behavioral_word_ratio', 'bert_embedding']

        for key in text_keys:
            if key in features_dict:
                text_features.append(features_dict[key])

        # 组合特征
        visual_feature = np.concatenate(visual_features) if visual_features else np.array([])
        audio_feature = np.concatenate(audio_features) if audio_features else np.array([])
        text_feature = np.concatenate(text_features) if text_features else np.array([])

        # 降维
        if len(visual_feature) > 0:
            visual_feature = reduce_feature_dimension(visual_feature, 128)
        else:
            visual_feature = np.zeros(128)

        if len(audio_feature) > 0:
            audio_feature = reduce_feature_dimension(audio_feature, 128)
        else:
            audio_feature = np.zeros(128)

        if len(text_feature) > 0:
            # 文本特征保持较高维度
            if len(text_feature) > 768:
                text_feature = reduce_feature_dimension(text_feature, 768)
            elif len(text_feature) < 768:
                # 填充到768维
                padding = np.zeros(768 - len(text_feature))
                text_feature = np.concatenate([text_feature, padding])
        else:
            text_feature = np.zeros(768)

        return visual_feature, audio_feature, text_feature

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class PositionalEncoding(nn.Module):
    """位置编码，用于Transformer模型"""
    def __init__(self, d_model, max_len=100):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        x = x + self.pe[:, :x.size(1), :]
        return x

class TemporalAttention(nn.Module):
    """时序注意力模块"""
    def __init__(self, hidden_dim, num_heads=4):
        super(TemporalAttention, self).__init__()
        self.multihead_attn = nn.MultiheadAttention(hidden_dim, num_heads, batch_first=True)
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Linear(hidden_dim * 4, hidden_dim)
        )
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        # x: [batch_size, seq_len, hidden_dim]
        attn_output, _ = self.multihead_attn(x, x, x)
        x = self.norm1(x + self.dropout(attn_output))
        ffn_output = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_output))
        return x

class LSTMEncoder(nn.Module):
    """LSTM编码器，用于时序特征提取"""
    def __init__(self, input_dim, hidden_dim, num_layers=2, bidirectional=True, dropout=0.2):
        super(LSTMEncoder, self).__init__()
        self.lstm = nn.LSTM(
            input_dim, 
            hidden_dim // 2 if bidirectional else hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=bidirectional,
            dropout=dropout if num_layers > 1 else 0
        )
        self.norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x):
        # x: [batch_size, seq_len, input_dim]
        output, (hidden, _) = self.lstm(x)
        # 使用最后一个时间步的隐藏状态
        if self.lstm.bidirectional:
            # 连接前向和后向的最后隐藏状态
            last_hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)
        else:
            last_hidden = hidden[-1]
        return self.norm(last_hidden), output

class CrossModalTemporalAttention(nn.Module):
    """跨模态时序注意力"""
    def __init__(self, hidden_dim, num_heads=4):
        super(CrossModalTemporalAttention, self).__init__()
        self.multihead_attn = nn.MultiheadAttention(hidden_dim, num_heads, batch_first=True)
        self.norm = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, query, key, value):
        # query, key, value: [batch_size, seq_len, hidden_dim]
        attn_output, attn_weights = self.multihead_attn(query, key, value)
        output = self.norm(query + self.dropout(attn_output))
        return output, attn_weights

class HierarchicalClassifier(nn.Module):
    """分层分类器，先判断是否有症状，再细分症状类型"""
    def __init__(self, input_dim, hidden_dim=128):
        super(HierarchicalClassifier, self).__init__()
        # 第一层：判断是否有症状
        self.presence_classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, 2)  # 二分类：有症状 vs 无症状
        )
        
        # 第二层：细分症状类型（仅当第一层预测有症状时）
        self.type_classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, 2)  # 二分类：特定症状 vs 其他症状
        )
        
    def forward(self, x, training=True):
        # 第一层：判断是否有症状
        presence_logits = self.presence_classifier(x)
        presence_probs = F.softmax(presence_logits, dim=1)
        
        # 第二层：细分症状类型
        type_logits = self.type_classifier(x)
        
        if training:
            # 训练时返回两个分类器的logits
            return presence_logits, type_logits
        else:
            # 推理时，根据第一层结果决定是否应用第二层
            has_symptom = (presence_probs[:, 1] > 0.5).float().unsqueeze(1)
            # 如果第一层预测无症状，则第二层输出也应该是无症状
            # 使用has_symptom作为门控，确保只有当第一层预测有症状时，第二层的预测才有效
            final_probs = presence_probs[:, 1].unsqueeze(1) * F.softmax(type_logits, dim=1)
            return final_probs

class TemporalEnhancedModel(nn.Module):
    """时序增强的多模态多症状识别模型"""
    def __init__(self, visual_dim=128, audio_dim=128, text_dim=768, hidden_dim=256, max_seq_len=100):
        super(TemporalEnhancedModel, self).__init__()
        
        # 位置编码
        self.visual_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)
        self.audio_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)
        self.text_pos_encoder = PositionalEncoding(hidden_dim, max_seq_len)
        
        # 特征编码器 - 静态特征
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.Linear(audio_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 时序编码器 - LSTM
        self.visual_lstm = LSTMEncoder(hidden_dim, hidden_dim)
        self.audio_lstm = LSTMEncoder(hidden_dim, hidden_dim)
        self.text_lstm = LSTMEncoder(hidden_dim, hidden_dim)
        
        # 时序注意力
        self.visual_temporal_attn = TemporalAttention(hidden_dim)
        self.audio_temporal_attn = TemporalAttention(hidden_dim)
        self.text_temporal_attn = TemporalAttention(hidden_dim)
        
        # 跨模态时序注意力
        self.v2a_attn = CrossModalTemporalAttention(hidden_dim)
        self.v2t_attn = CrossModalTemporalAttention(hidden_dim)
        self.a2v_attn = CrossModalTemporalAttention(hidden_dim)
        self.a2t_attn = CrossModalTemporalAttention(hidden_dim)
        self.t2v_attn = CrossModalTemporalAttention(hidden_dim)
        self.t2a_attn = CrossModalTemporalAttention(hidden_dim)
        
        # 融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(hidden_dim * 9, hidden_dim * 3),
            nn.LayerNorm(hidden_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim * 3, hidden_dim * 3),
            nn.Sigmoid()
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU()
        )
        
        # 残差连接
        self.residual = nn.Linear(hidden_dim * 3, hidden_dim)
        
        # 分层分类器 - 每种症状一个
        self.symptom_classifiers = nn.ModuleDict({
            '情绪症状': HierarchicalClassifier(hidden_dim),
            '躯体症状': HierarchicalClassifier(hidden_dim),
            '认知症状': HierarchicalClassifier(hidden_dim),
            '行为症状': HierarchicalClassifier(hidden_dim)
        })
        
        # 症状特定注意力
        self.symptom_attentions = nn.ModuleDict({
            '情绪症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '躯体症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '认知症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            ),
            '行为症状': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.Tanh(),
                nn.Linear(hidden_dim, 1),
                nn.Sigmoid()
            )
        })
        
    def _prepare_sequence(self, x, seq_len=10):
        """将静态特征转换为序列形式，以便进行时序处理"""
        batch_size = x.size(0)
        feature_dim = x.size(1)
        
        # 如果输入已经是序列形式，直接返回
        if len(x.shape) == 3:
            return x
            
        # 否则，创建一个假的序列
        # 通过添加少量噪声创建序列变化
        noise_scale = 0.05
        sequence = x.unsqueeze(1).expand(batch_size, seq_len, feature_dim)
        noise = torch.randn(batch_size, seq_len, feature_dim, device=x.device) * noise_scale
        sequence = sequence + noise
        
        return sequence
        
    def forward(self, visual, audio, text, training=True):
        batch_size = visual.size(0)
        
        # 1. 静态特征编码
        visual_encoded = self.visual_encoder(visual)
        audio_encoded = self.audio_encoder(audio)
        text_encoded = self.text_encoder(text)
        
        # 2. 准备序列数据（如果输入不是序列形式）
        visual_seq = self._prepare_sequence(visual_encoded)
        audio_seq = self._prepare_sequence(audio_encoded)
        text_seq = self._prepare_sequence(text_encoded)
        
        # 3. 添加位置编码
        visual_seq = self.visual_pos_encoder(visual_seq)
        audio_seq = self.audio_pos_encoder(audio_seq)
        text_seq = self.text_pos_encoder(text_seq)
        
        # 4. 时序编码 - LSTM
        visual_lstm_out, visual_seq_out = self.visual_lstm(visual_seq)
        audio_lstm_out, audio_seq_out = self.audio_lstm(audio_seq)
        text_lstm_out, text_seq_out = self.text_lstm(text_seq)
        
        # 5. 时序注意力
        visual_temporal = self.visual_temporal_attn(visual_seq_out)
        audio_temporal = self.audio_temporal_attn(audio_seq_out)
        text_temporal = self.text_temporal_attn(text_seq_out)
        
        # 6. 跨模态时序注意力
        v2a_out, _ = self.v2a_attn(visual_temporal, audio_temporal, audio_temporal)
        v2t_out, _ = self.v2t_attn(visual_temporal, text_temporal, text_temporal)
        a2v_out, _ = self.a2v_attn(audio_temporal, visual_temporal, visual_temporal)
        a2t_out, _ = self.a2t_attn(audio_temporal, text_temporal, text_temporal)
        t2v_out, _ = self.t2v_attn(text_temporal, visual_temporal, visual_temporal)
        t2a_out, _ = self.t2a_attn(text_temporal, audio_temporal, audio_temporal)
        
        # 7. 提取每个序列的表示（使用平均池化）
        v2a_feat = torch.mean(v2a_out, dim=1)
        v2t_feat = torch.mean(v2t_out, dim=1)
        a2v_feat = torch.mean(a2v_out, dim=1)
        a2t_feat = torch.mean(a2t_out, dim=1)
        t2v_feat = torch.mean(t2v_out, dim=1)
        t2a_feat = torch.mean(t2a_out, dim=1)
        
        # 8. 融合特征 - 包含所有交叉注意力结果
        concat_feat = torch.cat([
            visual_lstm_out, audio_lstm_out, text_lstm_out,
            v2a_feat, v2t_feat, a2v_feat, a2t_feat, t2v_feat, t2a_feat
        ], dim=1)
        
        gate = self.fusion_gate(concat_feat)
        
        # 9. 应用自适应门控
        hidden_dim = visual_lstm_out.size(1)
        gated_features = torch.cat([
            visual_lstm_out * gate[:, :hidden_dim],
            audio_lstm_out * gate[:, hidden_dim:2*hidden_dim],
            text_lstm_out * gate[:, 2*hidden_dim:3*hidden_dim]
        ], dim=1)
        
        # 10. 最终融合
        fused = self.fusion(gated_features)
        
        # 11. 残差连接
        residual_features = self.residual(torch.cat([visual_lstm_out, audio_lstm_out, text_lstm_out], dim=1))
        fused = fused + residual_features
        
        # 12. 症状特化分类 - 分层分类
        outputs = {}
        for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']:
            # 应用症状特定注意力
            symptom_attn = self.symptom_attentions[symptom](fused)
            symptom_features = fused * symptom_attn
            
            # 应用分层分类器
            if training:
                # 训练时返回两个分类器的logits
                presence_logits, type_logits = self.symptom_classifiers[symptom](symptom_features, training=True)
                outputs[symptom] = (presence_logits, type_logits)
            else:
                # 推理时返回最终预测
                final_probs = self.symptom_classifiers[symptom](symptom_features, training=False)
                outputs[symptom] = final_probs
        
        # 13. 转换输出为列表，保持与原模型兼容
        if training:
            # 训练时，只返回第二层分类器的输出（细分症状类型）
            output_list = [outputs[symptom][1] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
        else:
            # 推理时，返回最终预测概率
            output_list = [outputs[symptom] for symptom in ['情绪症状', '躯体症状', '认知症状', '行为症状']]
        
        return output_list

# 分层损失函数
class HierarchicalLoss(nn.Module):
    """分层损失函数，同时考虑症状存在性和症状类型"""
    def __init__(self, presence_weight=0.5, type_weight=0.5):
        super(HierarchicalLoss, self).__init__()
        self.presence_weight = presence_weight
        self.type_weight = type_weight
        self.ce_loss = nn.CrossEntropyLoss()
        
    def forward(self, outputs, targets):
        presence_logits, type_logits = outputs
        
        # 症状存在性损失 - 二分类：有症状(1) vs 无症状(0)
        presence_target = (targets > 0).long()
        presence_loss = self.ce_loss(presence_logits, presence_target)
        
        # 症状类型损失 - 二分类：特定症状 vs 其他
        type_loss = self.ce_loss(type_logits, targets)
        
        # 总损失 = 存在性损失 * 权重 + 类型损失 * 权重
        total_loss = self.presence_weight * presence_loss + self.type_weight * type_loss
        
        return total_loss

import os
import torch
import numpy as np
import argparse
from temporal_enhanced_model import TemporalEnhancedModel
from temporal_data_processor import prepare_temporal_data
from temporal_trainer import train_temporal_model, evaluate_temporal_model, save_results

def set_seed(seed):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="时序增强的多模态多症状识别模型")
    parser.add_argument("--data_dir", type=str, default="./shuju", help="特征数据目录")
    parser.add_argument("--label_file", type=str, default="./symptom_label_template.xlsx", help="标签文件路径")
    
    # 学术规范的训练参数
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.0005, help="初始学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-5, help="权重衰减系数")
    parser.add_argument("--patience", type=int, default=15, help="早停耐心值")
    parser.add_argument("--min_delta", type=float, default=0.001, help="早停最小改进阈值")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    
    # 时序模型参数
    parser.add_argument("--max_seq_len", type=int, default=100, help="最大序列长度")
    parser.add_argument("--hidden_dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--augment_prob", type=float, default=0.2, help="数据增强概率")
    
    # 损失函数参数
    parser.add_argument("--use_focal_loss", action="store_true", default=True, help="是否使用Focal Loss")
    parser.add_argument("--focal_loss_gamma", type=float, default=2.0, help="Focal Loss的gamma参数")
    parser.add_argument("--use_hierarchical_loss", action="store_true", default=True, help="是否使用分层损失")
    
    # 其他参数
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="训练设备")
    parser.add_argument("--output_dir", type=str, default="./temporal_results", help="输出目录")
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 要预测的症状列表
    symptom_types = ['情绪症状', '躯体症状', '认知症状', '行为症状']
    
    # 准备时序数据
    print("准备时序数据...")
    train_loader, val_loader, test_loader, symptom_types = prepare_temporal_data(
        data_dir=args.data_dir,
        label_file=args.label_file,
        symptom_types=symptom_types,
        max_seq_len=args.max_seq_len,
        batch_size=args.batch_size,
        augment_prob=args.augment_prob,
        seed=args.seed
    )
    
    # 创建时序增强模型
    print("创建时序增强模型...")
    model = TemporalEnhancedModel(
        visual_dim=128,  # 根据实际特征维度调整
        audio_dim=128,   # 根据实际特征维度调整
        text_dim=768,    # BERT嵌入维度
        hidden_dim=args.hidden_dim,
        max_seq_len=args.max_seq_len
    )
    
    model.to(device)
    print("模型已创建并移动到设备上")
    
    # 训练模型
    print("\n开始训练时序增强模型...")
    model, training_history = train_temporal_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        symptom_types=symptom_types,
        device=device,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        epochs=args.epochs,
        patience=args.patience,
        min_delta=args.min_delta,
        use_focal_loss=args.use_focal_loss,
        focal_loss_gamma=args.focal_loss_gamma,
        use_hierarchical_loss=args.use_hierarchical_loss
    )
    
    # 在测试集上评估模型
    print("\n在测试集上评估时序增强模型...")
    test_metrics = evaluate_temporal_model(
        model=model,
        test_loader=test_loader,
        symptom_types=symptom_types,
        device=device,
        optimize_thresholds=True
    )
    
    # 保存结果
    save_results(
        model=model,
        history=training_history,
        metrics=test_metrics,
        model_path=os.path.join(args.output_dir, "temporal_model.pth"),
        history_path=os.path.join(args.output_dir, "temporal_training_history.json"),
        metrics_path=os.path.join(args.output_dir, "temporal_test_results.json")
    )

if __name__ == "__main__":
    main()
